import request from '@/utils/request'

// 查询医院管理列表
export function listHospital(query) {
  return request({
    url: '/hospital',
    method: 'get',
    params: query
  })
}

// 当前账号组织机构能看到的医院下拉列表
export function listDoctorClientHospital(query) {
  return request({
    url: '/doctorClient/HospitalList',
    method: 'get',
    params: query
  })
}
// 当前账号组织机构能看到的医院下拉列表1
export function getlistDoctorClientHospital(query) {
  return request({
    url: '/doctorClient/toSelectHospita',
    method: 'post',
    params: query
  })
}
