<template>
  <div class="case-list-info">
    <!-- 无创单病 -->
     <div class="case-list-info">
      <div class="clinical-info-title">患者信息</div>
      <el-row style="width: 100%" :gutter="10" class="info-content">
        <el-col :span="6" v-for="item of baseInfos" :key="item.label">
          <div class="info-item-view"
            ><span class="item-title">{{ item.label }}: </span
            >{{ info[item.valueKey[0]] || (item.valueKey[1] ? info[item.valueKey[1]] : "-") || "-" }}</div
          >
        </el-col>
      </el-row>
      <div class="go-back-view">
      <el-button type="primary" plain @click="goBack">返回</el-button>
    </div>
  
     </div>
     <el-row>
        <el-col>
          <div>组学数数存储</div>
          <file-upload v-model="form.groupData" :pCode="pCode" fileType="7" />
        </el-col>
      </el-row>
  </div>

</template>

<script>
import FileUpload from "@/components/uploadFile";
import { handlelistServen } from '@/api/caseManagement';
export default {
  components: {
        FileUpload
    },
  props: {
    patientData: {
      type: Object,
    
    },
    patientTypeMap: {
      type: Object,
      default: () => {
        return {};
      },
    },
    uploadType: {
      type: Array,
      default: () => {
        return [];
      },
    },
    routerType: {
      type: String,
      default: "",
    },
  },
  watch: {
    patientData: {
      handler(newValue) {
        if(newValue){
          this.info = newValue;
        
          console.log(this.info,'2');
        }
       
       
      },
      deep: true,
      immediate: true,
    },
    $route: {
            handler(newValue) {
                this.pCode = newValue.query.pCode
            },
            deep: true,
            immediate: true
        }
  },
  data() {
    return {
      info:{},
      pCode:'',
      form:{groupData:[]},
      baseInfos: [
        { label: "姓名", valueKey: ["gname", "gName"] },
        { label: "年龄", valueKey: ["gage", "gAge"] },
        { label: "证件类型", valueKey: ["docType"] },
        { label: "证件号码", valueKey: ["idNum"] },
        { label: "联系方式", valueKey: ["gmobile", "gMobile"] },
        { label: "末次月经时间", valueKey: ["menstruationEndTime"] },
      ],
    };
  },
  computed: {
  
  },
  methods: {
    handlelist() {
      handlelistServen({
                pCode: this.pCode
            }).then(response => {
                console.log(response, 'ooo');
              this.form.groupData=response.data

            });
        },
    goBack() {
      const obj = { path: "/genomicsDataManagement" };
      this.$tab.closeOpenPage(obj);
    },
  },
};
</script>
