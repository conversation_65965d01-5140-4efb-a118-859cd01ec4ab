<template>
    <div class="case-list-info">
      <!-- 孕妇 -->
      <div class="clinical-info">
        <div class="clinical-info-title">患者信息</div>
        <el-form
          ref="infoform"
          :model="info"
          label-width="120px"
          style="width: 100%;"
        >
          <el-row
            :gutter="10"
            class="info-content"
          >
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">姓名: </span>{{ info.gname || info.gName }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">性别: </span>{{ info.gender }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">年龄: </span>{{ info.gage || info.gAge }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">患者类型: </span>{{ patientTypeMap[info.patientType] }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">出生日期: </span>{{ info.gbirthday || info.gBirthday }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">民族: </span>{{ info.gclan || info.gClan }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">籍贯: </span>{{ info.gplace || info.gPlace }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">孕周: </span>{{ info.gweek || info.gWeek }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">孕天: </span>{{ info.gday || info.gDay }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">末次月经时间: </span>{{ info.menstruationEndTime }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">月经周期: </span>{{ info.menstrualCycle }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">身高: </span>{{ info.gheight || info.gHeight }}cm</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">体重: </span>{{ info.gweight || info.gWeight }}kg</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">证件类型: </span>{{ info.docType }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">证件号码: </span>{{ info.idNum }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">联系方式: </span>{{ info.gmobile || info.gMobile }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">是否上保险: </span>{{ info.ginsure || info.ginsure === '1' || info.ginsure === 1 || info.gInsure || info.gInsure === '1' || info.gInsure === 1 ? '是' : '否' }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">家属名称: </span>{{ info.familyName }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">家属联系方式: </span>{{ info.familyMobile }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">紧急联系人: </span>{{ info.emergencyName }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">紧急联系人手机号: </span>{{ info.emergencyMobile }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">地址: </span>{{ info.addressDetails }}</div>
            </el-col>
            <el-col :span="6">
              <div class="info-item-view"><span class="item-title">医院名称: </span>{{ info.hospName }}</div>
            </el-col>
          </el-row>
        </el-form>
      </div>
  
      <div class="go-back-view">
        <el-button
          type="primary"
          plain
          @click="goBack"
        >返回</el-button>
        <el-button
          v-if="!detailDisable"
          type="primary"
          @click="submitForm"
        >保存</el-button>
      </div>
    </div>
  </template>
  
  <script>
  import { updateGravidainfo } from "@/api/caseManagement";
  
  import FileUpload from "@/components/FileUpload";
  
  export default {
    props: {
      patientData: {
        type: Object,
        default: () => {
          return {}
        }
      },
      patientTypeMap: {
        type: Object,
        default: () => {
          return {}
        }
      },
      uploadType: {
        type: Array,
        default: () => {
          return []
        }
      },
      routerType: {
        type: String,
        default: ''
      }
    },
    components: {
      FileUpload
    },
    data() {
      const structureTextVal = (rule, value, callback) => {
        if (this.form.asStatus && value === '') {
          callback(new Error("请填写异常说明"));
        } else {
          callback();
        }
      };
      const indicatorRiskTextVal = (rule, value, callback) => {
        if (this.form.asIndicatorRisk && value === '') {
          callback(new Error("请填写软指标高风险信息"));
        } else {
          callback();
        }
      };
      const surgicalTreatmentTextVal = (rule, value, callback) => {
        if (this.form.asSurgicalTreatment && value === '') {
          callback(new Error("请填写介入性手术治疗信息"));
        } else {
          callback();
        }
      };
  
      const buliangyunTextVal = (rule, value, callback) => {
        if (this.formNIPT.asBuliangyun && value === '') {
          callback(new Error("请填写不良孕产史说明"));
        } else {
          callback();
        }
      };
      const jiazuTextVal = (rule, value, callback) => {
        if (this.formNIPT.asJiazu && value === '') {
          callback(new Error("请填写家族遗传病说明"));
        } else {
          callback();
        }
      };
      const fuqiransetiTextVal = (rule, value, callback) => {
        if (this.formNIPT.asFuqiranseti && value === '') {
          callback(new Error("请填写夫妻双方染色体说明"));
        } else {
          callback();
        }
      };
      const ivfTextNIPTVal = (rule, value, callback) => {
        if (this.formNIPT.asIvf && value === '') {
          callback(new Error("请填写试管婴儿说明"));
        } else {
          callback();
        }
      };
      const ivfTimeNIPTVal = (rule, value, callback) => {
        if (this.formNIPT.asIvf && value === '') {
          callback(new Error("请选择试管植入日期"));
        } else {
          callback();
        }
      };
      const caoshengTextNIPTVal = (rule, value, callback) => {
        if (this.formNIPT.asCaosheng && value === '') {
          callback(new Error("请填写超声检查说明"));
        } else {
          callback();
        }
      };
      const xueyeshaichaTextNIPTVal = (rule, value, callback) => {
        if (this.formNIPT.asXueyeshaicha && value === '') {
          callback(new Error("请填写血清筛查说明"));
        } else {
          callback();
        }
      };
      const santiANIPTVal = (rule, value, callback) => {
        if (this.formNIPT.assanti21 && value === '') {
          callback(new Error("请填写21-三体风险值说明"));
        } else {
          callback();
        }
      };
      const santiBNIPTVal = (rule, value, callback) => {
        if (this.formNIPT.assanti18 && value === '') {
          callback(new Error("请填写18-三体风险值说明"));
        } else {
          callback();
        }
      };
      const santiCNIPTVal = (rule, value, callback) => {
        if (this.formNIPT.assanti13 && value === '') {
          callback(new Error("请填写13-三体风险值说明"));
        } else {
          callback();
        }
      };
      const yuyuechaungcishoushuTimeNIPTVal = (rule, value, callback) => {
        if (this.formNIPT.asYuyuechaungcishoushu && value === '') {
          callback(new Error("请选择预约日期"));
        } else {
          callback();
        }
      };
      const xibaoziliaoTextNIPTVal = (rule, value, callback) => {
        if (this.formNIPT.asXibaoziliao && value === '') {
          callback(new Error("请填写细胞治疗说明"));
        } else {
          callback();
        }
      };
      const zhongliuTextNIPTVal = (rule, value, callback) => {
        if (this.formNIPT.asZhongliu && value === '') {
          callback(new Error("请填写肿瘤患者说明"));
        } else {
          callback();
        }
      };
      const yichangshuxueTextNIPTVal = (rule, value, callback) => {
        if (this.formNIPT.asYichangshuxue && value === '') {
          callback(new Error("请填写异常输血说明"));
        } else {
          callback();
        }
      };
      const yizhishoushuTextNIPTVal = (rule, value, callback) => {
        if (this.formNIPT.asYizhishoushu && value === '') {
          callback(new Error("请填写移植手术说明"));
        } else {
          callback();
        }
      };
      const mianyizhiliaoTextNIPTVal = (rule, value, callback) => {
        if (this.formNIPT.asMianyizhiliao && value === '') {
          callback(new Error("请填写免疫治疗说明"));
        } else {
          callback();
        }
      };
  
      const chanqianTextCNVVal = (rule, value, callback) => {
        if (this.formCNV.asChanqian && value === '') {
          callback(new Error("请填写产前诊断说明"));
        } else {
          callback();
        }
      };
      const buliangTextCNVVal = (rule, value, callback) => {
        if (this.formCNV.asBuliang && value === '') {
          callback(new Error("请填写不良孕产史说明"));
        } else {
          callback();
        }
      };
      const jiazuTextCNVVal = (rule, value, callback) => {
        if (this.formCNV.asJiazu && value === '') {
          callback(new Error("请填写家族遗传病说明"));
        } else {
          callback();
        }
      };
      const fuqiTextCNVVal = (rule, value, callback) => {
        if (this.formCNV.asFuqi && value === '') {
          callback(new Error("请填写夫妻双方染色体说明"));
        } else {
          callback();
        }
      };
      const ivfTextCNVVal = (rule, value, callback) => {
        if (this.formCNV.asIvf && value === '') {
          callback(new Error("请填写试管婴儿说明"));
        } else {
          callback();
        }
      };
      // const isIVFtimeCNVVal = (rule, value, callback) => {
      //   if (this.formCNV.asIvf && value === '') {
      //     callback(new Error("请选择试管植入日期"));
      //   } else {
      //     callback();
      //   }
      // };
      const caoshengTextCNVVal = (rule, value, callback) => {
        if (this.formCNV.asCaosheng && value === '') {
          callback(new Error("请填写超声检查说明"));
        } else {
          callback();
        }
      };
      const serumTextCNVVal = (rule, value, callback) => {
        if (this.formCNV.asSerum && value === '') {
          callback(new Error("请填写血清筛查说明"));
        } else {
          callback();
        }
      };
      const santiACNVVal = (rule, value, callback) => {
        if (this.formCNV.assanti21 && value === '') {
          callback(new Error("请填写21-三体风险值说明"));
        } else {
          callback();
        }
      };
      const santiBCNVVal = (rule, value, callback) => {
        if (this.formCNV.assanti18 && value === '') {
          callback(new Error("请填写18-三体风险值说明"));
        } else {
          callback();
        }
      };
      const santiCCNVVal = (rule, value, callback) => {
        if (this.formCNV.assanti13 && value === '') {
          callback(new Error("请填写13-三体风险值说明"));
        } else {
          callback();
        }
      };
      const othenTextCNVVal = (rule, value, callback) => {
        if (this.formCNV.asOthen && value === '') {
          callback(new Error("请填写疾病信息"));
        } else {
          callback();
        }
      };
      return {
        info: {},
        form: {
          id: '',
          userId: '',
          patientId: '',
          pcode: '',
          asIndicatorRisk: false,
          indicatorRiskText: '',
          asSurgicalTreatment: false,
          surgicalTreatmentText: '',
          asStatus: '',
          structureText: '',
        },
        formRules: {
          structureText: [
            { required: true, validator: structureTextVal, trigger: "change" }
          ],
          indicatorRiskText: [
            { required: true, validator: indicatorRiskTextVal, trigger: "change" }
          ],
          surgicalTreatmentText: [
            { required: true, validator: surgicalTreatmentTextVal, trigger: "change" }
          ],
        },
        formNIPT: {
          id: '',
          userId: '',
          patientId: '',
          caiTime: '',
          shouTime: '',
          songText: '',
          pcode: '',
          yunNum:'',
          chanNum: '',
          asBuliangyun: false,
          buliangyunText: '',
          asJiazu: false,
          jiazuText: '',
          asFuqiranseti: false,
          fuqiransetiText: '',
          asPregnancy: '',
          pregnancyText: '',
          asIvf: false,
          ivfText: '',
          ivfTime: '',
          asCaosheng: false,
          caoshengText: '',
          asXueyeshaicha: false,
          xueyeshaichaText: '',
          shaichamoshi: '',
          santiType: '', // 三体 总的， '21,18,13' , 保存和 获取 都需要单独处理
          assanti21: false,
          santiA: '',
          assanti18: false,
          santiB: '',
          assanti13: false,
          santiC: '',
          asYuyuechaungcishoushu: false,
          yuyuechaungcishoushuTime: '',
          asXibaoziliao: false,
          xibaoziliaoText: '',
          asZhongliu: false,
          zhongliuText: '',
          asYichangshuxue: false,
          yichangshuxueText: '',
          asYizhishoushu: false,
          yizhishoushuText: '',
          asMianyizhiliao: false,
          mianyizhiliaoText: '',
          teshubeizhu: '',
        },
        formNIPTRules: {
          buliangyunText: [
            { required: true, validator: buliangyunTextVal, trigger: "change" }
          ],
          jiazuText: [
            { required: true, validator: jiazuTextVal, trigger: "change" }
          ],
          fuqiransetiText: [
            { required: true, validator: fuqiransetiTextVal, trigger: "change" }
          ],
          ivfText: [
            { required: true, validator: ivfTextNIPTVal, trigger: "change" }
          ],
          ivfTime: [
            { required: true, validator: ivfTimeNIPTVal, trigger: "change" }
          ],
          caoshengText: [
            { required: true, validator: caoshengTextNIPTVal, trigger: "change" }
          ],
          xueyeshaichaText: [
            { required: true, validator: xueyeshaichaTextNIPTVal, trigger: "change" }
          ],
          santiA: [
            { required: true, validator: santiANIPTVal, trigger: "change" }
          ],
          santiB: [
            { required: true, validator: santiBNIPTVal, trigger: "change" }
          ],
          santiC: [
            { required: true, validator: santiCNIPTVal, trigger: "change" }
          ],
          yuyuechaungcishoushuTime: [
            { required: true, validator: yuyuechaungcishoushuTimeNIPTVal, trigger: "change" }
          ],
          xibaoziliaoText: [
            { required: true, validator: xibaoziliaoTextNIPTVal, trigger: "change" }
          ],
          zhongliuText: [
            { required: true, validator: zhongliuTextNIPTVal, trigger: "change" }
          ],
          yichangshuxueText: [
            { required: true, validator: yichangshuxueTextNIPTVal, trigger: "change" }
          ],
          yizhishoushuText: [
            { required: true, validator: yizhishoushuTextNIPTVal, trigger: "change" }
          ],
          mianyizhiliaoText: [
            { required: true, validator: mianyizhiliaoTextNIPTVal, trigger: "change" }
          ],
        },
        formCNV: {
          id: '',
          userId: '',
          patientId: '',
          caiTime: '',
          shouTime: '',
          songText: '',
          pcode: '',
          yunNum: '',
          chanNum: '',
          liuType: '',
          asChanqian: '',
          chanqianText: '',
          zhusuText: '',
          asBuliang: false,
          buliangText: '',
          asJiazu: false,
          jiazuText: '',
          asFuqi: false,
          fuqiText: '',
          asPregnancy: '',
          pregnancyText: '',
          asIvf: false,
          ivfText: '',
          // isIVFtime: '',
          asCaosheng: false,
          caoshengText: '',
          asSerum: false,
          serumText: '',
          screeningModel: 0,
          santiType: '', // 三体 总的， '21,18,13' , 保存和 获取 都需要单独处理
          assanti21: false,
          santiA: '',
          assanti18: false,
          santiB: '',
          assanti13: false,
          santiC: '',
          hzhusuText: '',
          hteshubeizhu: '',
          asZhili: false,
          asXinzangbing: false,
          asFayuchihuan: false,
          asJiwuzhu: false,
          asZhitijixing: false,
          asZangqijixing: false,
          asShengzhiqijixing: false,
          asZibi: false,
          asOthen: false,
          othenText: '',
          othenDiagnosis: '',
          othenDiagnosisPerject: '',
          othenDiagnosisEnd: '',
          othenDiagnosisInfo: '',
          fileUsr: '',
        },
        formCNVRules: {
          chanqianText: [
            { required: true, validator: chanqianTextCNVVal, trigger: "change" }
          ],
          buliangText: [
            { required: true, validator: buliangTextCNVVal, trigger: "change" }
          ],
          jiazuText: [
            { required: true, validator: jiazuTextCNVVal, trigger: "change" }
          ],
          fuqiText: [
            { required: true, validator: fuqiTextCNVVal, trigger: "change" }
          ],
          ivfText: [
            { required: true, validator: ivfTextCNVVal, trigger: "change" }
          ],
          // isIVFtime: [
          //   { required: true, validator: isIVFtimeCNVVal, trigger: "change" }
          // ],
          caoshengText: [
            { required: true, validator: caoshengTextCNVVal, trigger: "change" }
          ],
          serumText: [
            { required: true, validator: serumTextCNVVal, trigger: "change" }
          ],
          santiA: [
            { required: true, validator: santiACNVVal, trigger: "change" }
          ],
          santiB: [
            { required: true, validator: santiBCNVVal, trigger: "change" }
          ],
          santiC: [
            { required: true, validator: santiCCNVVal, trigger: "change" }
          ],
          othenText: [
            { required: true, validator: othenTextCNVVal, trigger: "change" }
          ],
        },
        // screeningTypeOption: [
        //   { value: '1', label: '孕早期' },
        //   { value: '2', label: '中期' },
        //   { value: '3', label: '晚期' },
        // ]
      }
    },
    computed: {
      detailDisable() {
        return this.routerType !== 'supplement'
      }
    },
    watch: {
      patientData: {
        handler(newValue) {
          this.info = JSON.parse(JSON.stringify(newValue))
          const formInfo = newValue.gravidainfoVOList ? newValue.gravidainfoVOList[0] : {}
          const formNIPTInfo = newValue.gravidaniptVOList ? newValue.gravidaniptVOList[0] : {}
          const formCNVInfo = newValue.gravidacnvVOList ? newValue.gravidacnvVOList[0] : {}
          const santiNIPTData = {
            assanti21: false,
            assanti18: false,
            assanti13: false,
          }
          const santiCNVData = {
            assanti21: false,
            assanti18: false,
            assanti13: false,
          }
          if (Object.keys(formNIPTInfo).length > 0) {
            if (!formNIPTInfo.santiType) {
              formNIPTInfo.santiType = ''
            }
            if (formNIPTInfo.santiType.length > 0) {
              const arr = formNIPTInfo.santiType.split(',')
              arr.forEach(ele => {
                santiNIPTData[`assanti${ele}`] = true
              })
            }
          }
          if (Object.keys(formCNVInfo).length > 0) {
            if (!formCNVInfo.santiType) {
              formCNVInfo.santiType = ''
            }
            if (formCNVInfo.santiType.length > 0) {
              const arr = formCNVInfo.santiType.split(',')
              arr.forEach(ele => {
                santiCNVData[`assanti${ele}`] = true
              })
            }
          }
          this.form = { ...this.form, ...formInfo, ...{
            patientId: newValue.id,
            userId: newValue.userId,
          }}
          this.formNIPT = { ...this.formNIPT, ...formNIPTInfo, ...santiNIPTData, ...{
            patientId: newValue.id,
            userId: newValue.userId,
          }}
          this.formCNV = { ...this.formCNV, ...formCNVInfo, ...santiCNVData, ...{
            patientId: newValue.id,
            userId: newValue.userId,
          }}
        },
        deep: true,
        immediate: true
      }
    },
    methods: {
      submitForm() {
        Promise.all([this.formValidate('form'), this.formValidate('formNIPT'), this.formValidate('formCNV')]).then(() => {
          let santiTypeNIPTArr = []
          let santiTypeCNVArr = []
          const santiArr = [21, 18, 13]
          santiArr.forEach(ele => {
            if (this.formNIPT[`assanti${ele}`]) {
              santiTypeNIPTArr.push(ele)
            }
            if (this.formCNV[`assanti${ele}`]) {
              santiTypeCNVArr.push(ele)
            }
          })
          const params = {
            patientId: this.patientData.id,
            pcode: this.patientData.pcode,
            userId: this.patientData.userId,
            gravidainfoDTO: this.form,
            gravidaniptDTO: { ...this.formNIPT, ...{
              santiType: santiTypeNIPTArr.join()
            }},
            gravidacnvDTO: { ...this.formCNV, ...{
              santiType: santiTypeCNVArr.join()
            }}
          }
          updateGravidainfo(params).then(res => {
            this.$modal.msgSuccess("修改成功");
            setTimeout(() => {
              this.goBack()
            }, 1000);
          })
        }).catch(() => {
          this.$modal.msgError("请完善病历信息");
        })
      },
      formValidate(formName) {
        return new Promise((resolve, reject) => {
          this.$refs[formName].validate(valid => {
            if (valid) {
              resolve()
            } else {
              reject()
            }
          });
        })
      },
      goBack() {
        const obj = { path: "/caseManagement/case_list" };
        this.$tab.closeOpenPage(obj);
      }
    }
  }
  </script>
  