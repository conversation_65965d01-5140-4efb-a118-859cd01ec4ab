<template>
  <div class="case-list-info">
    <!-- 新生儿 -->
    <div class="clinical-info">
      <div class="clinical-info-title">患者信息</div>
      <el-form
        ref="infoform"
        :model="info"
        label-width="120px"
        style="width: 100%;"
      >
        <el-row :gutter="10" class="info-content">
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲姓名: </span>{{ info.motherName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">性别: </span>{{ info.gender }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">年龄: </span>{{ info.motherAge }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">患者类型: </span>{{ patientTypeMap[info.patientType] }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">出生日期: </span>{{ info.gbirthday || info.gBirthday }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">民族: </span>{{ info.gclan || info.gClan }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">证件类型: </span>{{ info.motherType }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">证件号码: </span>{{ info.motherNum }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">籍贯: </span>{{ info.gplace || info.gPlace }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">联系方式: </span>{{ info.motherMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">紧急联系人: </span>{{ info.emergencyName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">紧急联系人手机号: </span>{{ info.emergencyMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">医院名称: </span>{{ info.hospName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">身高: </span>{{ info.gheight || info.gHeight }}cm</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">体重: </span>{{ info.gweight || info.gWeight }}kg</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">地址: </span>{{ info.addressDetails }}</div>
          </el-col>
        </el-row>
      </el-form>
    </div>

  
    <div class="go-back-view">
      <el-button
        type="primary"
        plain
        @click="goBack"
      >返回</el-button>
     
    </div>
  </div>
</template>

<script>
import { updateNeonate } from "@/api/caseManagement";

import FileUpload from "@/components/FileUpload";

export default {
  props: {
    patientData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    patientTypeMap: {
      type: Object,
      default: () => {
        return {}
      }
    },
    uploadType: {
      type: Array,
      default: () => {
        return []
      }
    },
    routerType: {
      type: String,
      default: ''
    }
  },
  components: {
    FileUpload
  },
  data() {
    const jiancebingVal = (rule, value, callback) => {
      if (this.form.asYichang && value === '') {
        callback(new Error("请填写异常情况"));
      } else {
        callback();
      }
    };
    const jiazuTextVal = (rule, value, callback) => {
      if (this.form.asJiazu && value === '') {
        callback(new Error("请填写家族遗传病说明"));
      } else {
        callback();
      }
    };
    const shuxueTimeVal = (rule, value, callback) => {
      if (this.formCAH.asShuxue && value === '') {
        callback(new Error("请选择最近一次输血日期"));
      } else {
        callback();
      }
    };
    const xibaozhiliaoTextVal = (rule, value, callback) => {
      if (this.formCAH.asXibaozhiliao  && value === '') {
        callback(new Error("请填写细胞治疗说明"));
      } else {
        callback();
      }
    };
    const yidongshoushuTextVal = (rule, value, callback) => {
      if (this.formCAH.asYidongshoushu  && value === '') {
        callback(new Error("请填写移植手术说明"));
      } else {
        callback();
      }
    };
    const jiazuTextCAHVal = (rule, value, callback) => {
      if (this.formCAH.asJiazu  && value === '') {
        callback(new Error("请填写家族遗传病说明"));
      } else {
        callback();
      }
    };
    return {
      info: {},
      form: {
        id: '',
        userId: '',
        patientId: '',
        caiTime: '',
        shouTime: '',
        songText: '',
        asZaochan: '',
        birthWeight: '',
        asTingli: false,
        asErlong: false,
        asXinzangbing: false,
        asGuanjie: false,
        asYichang: '',
        jiancebing: '',
        chuchaEnd: '',
        fuchaEnd: '',
        linchaungZengduan: '',
        asJiazu: false,
        jiazuText: '',
        teshubeizhu: '',
      },
      formRules: {
        jiancebing: [
          { required: true, validator: jiancebingVal, trigger: "change" }
        ],
        jiazuText: [
          { required: true, validator: jiazuTextVal, trigger: "change" }
        ],
      },
      formCAH: {
        id: '',
        userId: '',
        patientId: '',
        caiTime: '',
        shouTime: '',
        songText: '',
        pcode: '',
        shenghuajiance: '',
        othenJiance: '',
        linchuangText: '',
        asShuxue: '',
        shuxueTime: '',
        asXibaozhiliao: false,
        xibaozhiliaoText: '',
        asYidongshoushu: false,
        yidongshoushuText: '',
        asJiazu: false,
        jiazuText: '',
        teshubeizhu: '',
        fileUrl: '',
      },
      formCAHRules: {
        shuxueTime: [
          { required: true, validator: shuxueTimeVal, trigger: "change" }
        ],
        xibaozhiliaoText: [
          { required: true, validator: xibaozhiliaoTextVal, trigger: "change" }
        ],
        yidongshoushuText: [
          { required: true, validator: yidongshoushuTextVal, trigger: "change" }
        ],
        jiazuText: [
          { required: true, validator: jiazuTextCAHVal, trigger: "change" }
        ],
      }
    }
  },
  computed: {
    detailDisable() {
      return this.routerType !== 'supplement'
    }
  },
  watch: {
    patientData: {
      handler(newValue) {
        this.info = JSON.parse(JSON.stringify(newValue))
        const formInfo = newValue.neonateinfoVOList ? newValue.neonateinfoVOList[0] : {}
        const formCAHInfo = newValue.neonatecahVOList ? newValue.neonatecahVOList[0] : {}
        this.form = { ...this.form, ...formInfo, ...{
          patientId: newValue.id,
          userId: newValue.userId,
        }}
        this.formCAH = { ...this.formCAH, ...formCAHInfo, ...{
          patientId: newValue.id,
          userId: newValue.userId,
        }}
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    submitForm() {
      Promise.all([this.formValidate('form'), this.formValidate('formCAH')]).then(() => {
        const params = {
          patientId: this.patientData.id,
          pcode: this.patientData.pcode,
          userId: this.patientData.userId,
          neonateinfoDTO: this.form,
          neonatecahDTO: this.formCAH
        }
        updateNeonate(params).then(res => {
          this.$modal.msgSuccess("修改成功");
          setTimeout(() => {
            this.goBack()
          }, 1000);
        })
      }).catch(() => {
        this.$modal.msgError("请完善病历信息");
      })
    },
    formValidate(formName) {
      return new Promise((resolve, reject) => {
        this.$refs[formName].validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        });
      })
    },
    goBack() {
      const obj = { path: "/genomicsDataManagement/case_list" };
      this.$tab.closeOpenPage(obj);
    }
  }
}
</script>
