<template>
  <div class="case-list-info">
    <!-- 新生儿 -->
    <div class="clinical-info">
      <div class="clinical-info-title">患者信息</div>
      <el-form
        ref="infoform"
        :model="info"
        label-width="120px"
        style="width: 100%;"
      >
        <el-row :gutter="10" class="info-content">
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲姓名: </span>{{ info.motherName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">性别: </span>{{ info.gender }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">年龄: </span>{{ info.motherAge }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">患者类型: </span>{{ patientTypeMap[info.patientType] }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">出生日期: </span>{{ info.gbirthday || info.gBirthday }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">民族: </span>{{ info.gclan || info.gClan }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">证件类型: </span>{{ info.motherType }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">证件号码: </span>{{ info.motherNum }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">籍贯: </span>{{ info.gplace || info.gPlace }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">联系方式: </span>{{ info.motherMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">紧急联系人: </span>{{ info.emergencyName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">紧急联系人手机号: </span>{{ info.emergencyMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">医院名称: </span>{{ info.hospName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">身高: </span>{{ info.gheight || info.gHeight }}cm</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">体重: </span>{{ info.gweight || info.gWeight }}kg</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">地址: </span>{{ info.addressDetails }}</div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="clinical-info">
      <div class="clinical-info-title">临床信息</div>
      <el-form
        ref="form"
        :model="form"
        :rules="formRules"
        label-width="120px"
        style="width: 100%;"
      >
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="采样时间：">
              <el-date-picker
                clearable
                v-model="form.caiTime"
                type="datetime"
                :disabled="detailDisable"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收样时间：">
              <el-date-picker
                clearable
                v-model="form.shouTime"
                type="datetime"
                :disabled="detailDisable"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="送检材料：">
              <el-input v-model="form.songText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-form-item label="是否早产：">
              <el-radio-group
                v-model="form.asZaochan"
                :disabled="detailDisable"
                class="mr20"
              >
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="出生体重："
              class="flex"
            >
              <el-input v-model="form.birthWeight" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="form.asTingli"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >新生儿听力筛查</el-checkbox>
            <el-checkbox
              v-model="form.asErlong"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >新生儿耳聋筛查</el-checkbox>
            <el-checkbox
              v-model="form.asXinzangbing"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >新生儿先天性心脏病筛查</el-checkbox>
            <el-checkbox
              v-model="form.asGuanjie"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >新生儿发育性髋关节筛查</el-checkbox>
          </el-col>

          <el-col
            :span="24"
            class="display-flex"
          >
            <el-form-item label="新生儿质谱是否异常：" label-width="160px">
              <el-radio-group
                v-model="form.asYichang"
                :disabled="detailDisable"
                class="mr20"
              >
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="form.asYichang"
              label="检测几种病："
              prop="jiancebing"
              class="flex"
            >
              <el-input v-model="form.jiancebing" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="初筛结果：">
              <el-input v-model="form.chuchaEnd" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="复筛结果：">
              <el-input v-model="form.fuchaEnd" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="临床表型/诊断：">
              <el-input v-model="form.linchaungZengduan" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="form.asJiazu"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >家族遗传病</el-checkbox>
            <el-form-item
              v-if="form.asJiazu"
              label="家族遗传病说明："
              prop="jiazuText"
              label-width="140px"
              class="flex"
            >
              <el-input v-model="form.jiazuText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="特殊备注：">
              <el-input v-model="form.teshubeizhu" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="clinical-info">
      <div class="clinical-info-title">三代测序CAH</div>
      <el-form
        ref="formCAH"
        :model="formCAH"
        :rules="formCAHRules"
        label-width="120px"
        style="width: 100%;"
      >
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="采样时间：">
              <el-date-picker
                clearable
                v-model="formCAH.caiTime"
                type="datetime"
                :disabled="detailDisable"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收样时间：">
              <el-date-picker
                clearable
                v-model="formCAH.shouTime"
                type="datetime"
                :disabled="detailDisable"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="送检材料：">
              <el-input v-model="formCAH.songText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="病历门诊号：">
              <el-input v-model="formCAH.pcode" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="生化检测：">
              <el-input v-model="formCAH.shenghuajiance" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="其他生化检测结果：" label-width="150px">
              <el-input v-model="formCAH.othenJiance" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="临床信息描述：">
              <el-input v-model="formCAH.linchuangText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-form-item label="是否有输血史：">
              <el-radio-group
                v-model="formCAH.asShuxue"
                :disabled="detailDisable"
                class="mr20"
              >
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="formCAH.asShuxue"
              label="最近一次输血日期："
              prop="shuxueTime"
              label-width="190px"
              class="flex"
            >
              <el-date-picker
                clearable
                v-model="formCAH.shuxueTime"
                :disabled="detailDisable"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>

          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCAH.asXibaozhiliao"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >细胞治疗</el-checkbox>
            <el-form-item
              v-if="formCAH.asXibaozhiliao"
              label="细胞治疗说明："
              prop="xibaozhiliaoText"
              class="flex"
            >
              <el-input v-model="formCAH.xibaozhiliaoText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCAH.asYidongshoushu"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >移植手术</el-checkbox>
            <el-form-item
              v-if="formCAH.asYidongshoushu"
              label="移植手术说明："
              prop="yidongshoushuText"
              class="flex"
            >
              <el-input v-model="formCAH.yidongshoushuText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCAH.asJiazu"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >家族遗传病</el-checkbox>
            <el-form-item
              v-if="formCAH.asJiazu"
              label="家族遗传病说明："
              prop="jiazuText"
              label-width="140px"
              class="flex"
            >
              <el-input v-model="formCAH.jiazuText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="特殊备注：">
              <el-input v-model="formCAH.teshubeizhu" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="16">
            <el-form-item label="知情同意书上传：" label-width="140px">
              <file-upload v-model="formCAH.fileUrl" :fileType="uploadType" :disabled="detailDisable" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="go-back-view">
      <el-button
        type="primary"
        plain
        @click="goBack"
      >返回</el-button>
      <el-button
        v-if="!detailDisable"
        type="primary"
        @click="submitForm"
      >保存</el-button>
    </div>
  </div>
</template>

<script>
import { updateNeonate } from "@/api/caseManagement";

import FileUpload from "@/components/FileUpload";

export default {
  props: {
    patientData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    patientTypeMap: {
      type: Object,
      default: () => {
        return {}
      }
    },
    uploadType: {
      type: Array,
      default: () => {
        return []
      }
    },
    routerType: {
      type: String,
      default: ''
    }
  },
  components: {
    FileUpload
  },
  data() {
    const jiancebingVal = (rule, value, callback) => {
      if (this.form.asYichang && value === '') {
        callback(new Error("请填写异常情况"));
      } else {
        callback();
      }
    };
    const jiazuTextVal = (rule, value, callback) => {
      if (this.form.asJiazu && value === '') {
        callback(new Error("请填写家族遗传病说明"));
      } else {
        callback();
      }
    };
    const shuxueTimeVal = (rule, value, callback) => {
      if (this.formCAH.asShuxue && value === '') {
        callback(new Error("请选择最近一次输血日期"));
      } else {
        callback();
      }
    };
    const xibaozhiliaoTextVal = (rule, value, callback) => {
      if (this.formCAH.asXibaozhiliao  && value === '') {
        callback(new Error("请填写细胞治疗说明"));
      } else {
        callback();
      }
    };
    const yidongshoushuTextVal = (rule, value, callback) => {
      if (this.formCAH.asYidongshoushu  && value === '') {
        callback(new Error("请填写移植手术说明"));
      } else {
        callback();
      }
    };
    const jiazuTextCAHVal = (rule, value, callback) => {
      if (this.formCAH.asJiazu  && value === '') {
        callback(new Error("请填写家族遗传病说明"));
      } else {
        callback();
      }
    };
    return {
      info: {},
      form: {
        id: '',
        userId: '',
        patientId: '',
        caiTime: '',
        shouTime: '',
        songText: '',
        asZaochan: '',
        birthWeight: '',
        asTingli: false,
        asErlong: false,
        asXinzangbing: false,
        asGuanjie: false,
        asYichang: '',
        jiancebing: '',
        chuchaEnd: '',
        fuchaEnd: '',
        linchaungZengduan: '',
        asJiazu: false,
        jiazuText: '',
        teshubeizhu: '',
      },
      formRules: {
        jiancebing: [
          { required: true, validator: jiancebingVal, trigger: "change" }
        ],
        jiazuText: [
          { required: true, validator: jiazuTextVal, trigger: "change" }
        ],
      },
      formCAH: {
        id: '',
        userId: '',
        patientId: '',
        caiTime: '',
        shouTime: '',
        songText: '',
        pcode: '',
        shenghuajiance: '',
        othenJiance: '',
        linchuangText: '',
        asShuxue: '',
        shuxueTime: '',
        asXibaozhiliao: false,
        xibaozhiliaoText: '',
        asYidongshoushu: false,
        yidongshoushuText: '',
        asJiazu: false,
        jiazuText: '',
        teshubeizhu: '',
        fileUrl: '',
      },
      formCAHRules: {
        shuxueTime: [
          { required: true, validator: shuxueTimeVal, trigger: "change" }
        ],
        xibaozhiliaoText: [
          { required: true, validator: xibaozhiliaoTextVal, trigger: "change" }
        ],
        yidongshoushuText: [
          { required: true, validator: yidongshoushuTextVal, trigger: "change" }
        ],
        jiazuText: [
          { required: true, validator: jiazuTextCAHVal, trigger: "change" }
        ],
      }
    }
  },
  computed: {
    detailDisable() {
      return this.routerType !== 'supplement'
    }
  },
  watch: {
    patientData: {
      handler(newValue) {
        this.info = JSON.parse(JSON.stringify(newValue))
        const formInfo = newValue.neonateinfoVOList ? newValue.neonateinfoVOList[0] : {}
        const formCAHInfo = newValue.neonatecahVOList ? newValue.neonatecahVOList[0] : {}
        this.form = { ...this.form, ...formInfo, ...{
          patientId: newValue.id,
          userId: newValue.userId,
        }}
        this.formCAH = { ...this.formCAH, ...formCAHInfo, ...{
          patientId: newValue.id,
          userId: newValue.userId,
        }}
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    submitForm() {
      Promise.all([this.formValidate('form'), this.formValidate('formCAH')]).then(() => {
        const params = {
          patientId: this.patientData.id,
          pcode: this.patientData.pcode,
          userId: this.patientData.userId,
          neonateinfoDTO: this.form,
          neonatecahDTO: this.formCAH
        }
        updateNeonate(params).then(res => {
          this.$modal.msgSuccess("修改成功");
          setTimeout(() => {
            this.goBack()
          }, 1000);
        })
      }).catch(() => {
        this.$modal.msgError("请完善病历信息");
      })
    },
    formValidate(formName) {
      return new Promise((resolve, reject) => {
        this.$refs[formName].validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        });
      })
    },
    goBack() {
      const obj = { path: "/caseManagement/case_list" };
      this.$tab.closeOpenPage(obj);
    }
  }
}
</script>
