<template>
  <div class="case-list-info">
    <!-- 携带者 -->
    <div class="clinical-info">
      <div class="clinical-info-title">患者信息</div>
      <el-form
        ref="infoform"
        :model="info"
        label-width="120px"
        style="width: 100%;"
      >
        <el-row
          :gutter="10"
          class="info-content"
        >
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲姓名: </span>{{ info.motherName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲年龄: </span>{{ info.motherAge }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲证件类型: </span>{{ info.motherType }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲证件号码: </span>{{ info.motherNum }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">患者类型: </span>{{ patientTypeMap[info.patientType] }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲联系方式: </span>{{ info.motherMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲出生日期: </span>{{ info.motherBirthday }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲民族: </span>{{ info.motherClan }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲籍贯: </span>{{ info.motherPlace }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲是否上保险: </span>{{ info.motherInsure || info.motherInsure === '1' || info.motherInsure === 1 ? '是' : '否' }}</div>
          </el-col>
        </el-row>
        <el-row
          :gutter="10"
          class="info-content"
        >
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲姓名: </span>{{ info.fatherName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲年龄: </span>{{ info.fatherAge }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲证件类型: </span>{{ info.fatherType }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲证件号码: </span>{{ info.fatherNum }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲联系方式: </span>{{ info.fatherMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲出生日期: </span>{{ info.fatherBirthday }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲民族: </span>{{ info.fatherClan }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲籍贯: </span>{{ info.fatherPlace }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲是否上保险: </span>{{ info.fatherInsure || info.fatherInsure === '1' || info.fatherInsure === 1 ? '是' : '否' }}</div>
          </el-col>
        </el-row>
      </el-form>
    </div>

   
    <div class="go-back-view">
      <el-button
        type="primary"
        plain
        @click="goBack"
      >返回</el-button>
     
    </div>
  </div>
</template>

<script>
import { updateClinicalinformation, getServiceCSData } from "@/api/caseManagement";

import FileUpload from "@/components/FileUpload";

export default {
  props: {
    patientData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    patientTypeMap: {
      type: Object,
      default: () => {
        return {}
      }
    },
    uploadType: {
      type: Array,
      default: () => {
        return []
      }
    },
    routerType: {
      type: String,
      default: ''
    }
  },
  components: {
    FileUpload
  },
  data() {
    const cellTherapyTextVal = (rule, value, callback) => {
      if (this.form.cellTherapy && value === '') {
        callback(new Error("请填写细胞治疗说明"));
      } else {
        callback();
      }
    };
    const rhealthTextVal = (rule, value, callback) => {
      if (this.form.rHealthType && value === '') {
        callback(new Error("请填写夫妻亲缘关系"));
      } else {
        callback();
      }
    };
    // const rjzqzVal = (rule, value, callback) => {
    //   if (this.form.asrjzqz && value === '') {
    //     callback(new Error("请选择是否诊断家族遗传病史"));
    //   } else {
    //     callback();
    //   }
    // };
    const rjzqzTextVal = (rule, value, callback) => {
      if (this.form.rjzqz && value === '') {
        callback(new Error("请填写家族遗传病史诊断描述"));
      } else {
        callback();
      }
    };
    const rfqTextVal = (rule, value, callback) => {
      if (this.form.rfqHealth && value === '') {
        callback(new Error("请填写姓名及检测结果"));
      } else {
        callback();
      }
    };
    const selectrPoorPregnancyVal = (rule, value, callback) => {
      if (this.form.asrPoorPregnancy && value.length === 0) {
        callback(new Error("请选择"));
      } else {
        callback();
      }
    };
    const rpoorTextVal = (rule, value, callback) => {
      if (this.form.asrPoorPregnancy && value === '') {
        callback(new Error("请填写不良孕产史描述"));
      } else {
        callback();
      }
    };
    const asGravidaWeekVal = (rule, value, callback) => {
      if (this.form.asGravida && value === '') {
        callback(new Error("请填写孕周"));
      } else {
        callback();
      }
    };
    const asGravidaDayVal = (rule, value, callback) => {
      if (this.form.asGravida && value === '') {
        callback(new Error("请填写孕天"));
      } else {
        callback();
      }
    };
    const asBabyTextVal = (rule, value, callback) => {
      if (this.form.asBaby && value === '') {
        callback(new Error("请填写胎儿异常描述"));
      } else {
        callback();
      }
    };
    const transplantTextVal = (rule, value, callback) => {
      if (this.form.transplant && value === '') {
        callback(new Error("请填写移植手术说明"));
      } else {
        callback();
      }
    };
    const bloodTextVal = (rule, value, callback) => {
      if (this.form.bloodOk && value === '') {
        callback(new Error("请填写异常输血说明"));
      } else {
        callback();
      }
    };
    const spouseCodeVal = (rule, value, callback) => {
      if (this.form.spouseHave && value === '') {
        callback(new Error("请填写检测编号"));
      } else {
        callback();
      }
    };
    const spouseResultsVal = (rule, value, callback) => {
      if (this.form.spouseHave && value === '') {
        callback(new Error("请填写检测结果"));
      } else {
        callback();
      }
    };
    return {
      info: {},
      isPregnantOption: [
        { value: true, label: '是' },
        { value: false, label: '否' },
      ],
      form: {
        id: '',
        userId: '',
        patientId: '',
        sampleStart: '',
        sampleEnd: '',
        inspectionMaterials: '',
        inspectionDepartment: '',
        inspectionDoctor:'',
        rHealthType: null,
        rhealthText: '',
        // asrjzqz: false,
        rjzqz: '',
        rjzqzText: '',
        rfqHealth: false,
        rfqText: '',
        asrPoorPregnancy: false, // 不良孕产史 是否有数据勾选，有为 true； 无为 false
        selectrPoorPregnancy: [],
        rPoorPregnancy: '', // 最后要将 selectrPoorPregnancy.join()
        rpoorText: '',
        rAuxiliary: false,
        rDonor: false,
        asGravida: '',
        asGravidaWeek: '',
        asGravidaDay: '',
        asBaby: '',
        asBabyText: '',
        cellTherapy: false,
        cellTherapyText: '',
        transplant: false,
        transplantText: '',
        bloodOk: false,
        bloodText: '',
        specialRemarks: '',
        spouseHave: '',
        spouseCode: '',
        spouseResults: '',
        proveUrl: '', // 知情同意书，统一修改
        asSync: '',
      },
      formRules: {
        cellTherapyText: [
          { required: true, validator: cellTherapyTextVal, trigger: "change" }
        ],
        rhealthText: [
          { required: true, validator: rhealthTextVal, trigger: "change" }
        ],
        // rjzqz: [
        //   { required: true, validator: rjzqzVal, trigger: "change" }
        // ],
        rjzqzText: [
          { required: true, validator: rjzqzTextVal, trigger: "change" }
        ],
        rfqText: [
          { required: true, validator: rfqTextVal, trigger: "change" }
        ],
        selectrPoorPregnancy: [
          { required: true, validator: selectrPoorPregnancyVal, trigger: "change" }
        ],
        rpoorText: [
          { required: true, validator: rpoorTextVal, trigger: "change" }
        ],
        asGravidaWeek: [
          { required: true, validator: asGravidaWeekVal, trigger: "change" }
        ],
        asGravidaDay: [
          { required: true, validator: asGravidaDayVal, trigger: "change" }
        ],
        asBabyText: [
          { required: true, validator: asBabyTextVal, trigger: "change" }
        ],
        transplantText: [
          { required: true, validator: transplantTextVal, trigger: "change" }
        ],
        bloodText: [
          { required: true, validator: bloodTextVal, trigger: "change" }
        ],
        spouseCode: [
          { required: true, validator: spouseCodeVal, trigger: "change" }
        ],
        spouseResults: [
          { required: true, validator: spouseResultsVal, trigger: "change" }
        ],
      },
      variationResult: [],
      detectionResult: [],
      testingReportSample: {},
      testingReportResult: {}
    }
  },
  computed: {
    detailDisable() {
      return this.routerType !== 'supplement'
    }
  },
  watch: {
    patientData: {
      handler(newValue) {
        if (Object.keys(newValue).length > 0) {
          this.getServiceCSDataRequest(newValue.pCode)
          this.info = JSON.parse(JSON.stringify(newValue))
          const formInfo = newValue.clinicalinformationVOList ? newValue.clinicalinformationVOList[0] : {}
          let data = {
            patientId: newValue.id,
            userId: newValue.userId,
          }
          if (Object.keys(formInfo).length > 0 && formInfo.rPoorPregnancy) {
            data = { ...data, ...{
              asrPoorPregnancy: formInfo.rPoorPregnancy.length > 0,
              selectrPoorPregnancy: formInfo.rPoorPregnancy.split(',')
            }}
          }
          this.form = { ...this.form, ...formInfo, ...data }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getServiceCSDataRequest(id) {
      getServiceCSData(id).then((response) => {
        this.testingReportSample = response.data.sample;
        this.testingReportResult = response.data.testResult;
      });
    },
    handleDownload() {

    },
    handlePreview() {

    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const params = JSON.parse(JSON.stringify(this.form))
          params.rPoorPregnancy = this.form.selectrPoorPregnancy.join()
          delete params['asrPoorPregnancy']
          delete params['selectrPoorPregnancy']
          updateClinicalinformation(params).then(res => {
            this.$modal.msgSuccess("修改成功");
            setTimeout(() => {
              this.goBack()
            }, 1000);
          })
        }
      });
    },
    goBack() {
      const obj = { path: "/genomicsDataManagement/case_list" };
      this.$tab.closeOpenPage(obj);
    }
  }
}
</script>
