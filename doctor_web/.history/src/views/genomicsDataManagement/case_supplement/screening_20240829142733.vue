<template>
  <div>
    <div class="case-list-info">
    <!-- 携带者 -->
    <div class="clinical-info">
      <div class="clinical-info-title">患者信息</div>
      <el-form
        ref="infoform"
        :model="info"
        label-width="120px"
        style="width: 100%;"
      >
        <el-row
          :gutter="10"
          class="info-content"
        >
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲姓名: </span>{{ info.motherName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲年龄: </span>{{ info.motherAge }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲证件类型: </span>{{ info.motherType }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲证件号码: </span>{{ info.motherNum }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">患者类型: </span>{{ patientTypeMap[info.patientType] }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲联系方式: </span>{{ info.motherMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲出生日期: </span>{{ info.motherBirthday }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲民族: </span>{{ info.motherClan }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲籍贯: </span>{{ info.motherPlace }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲是否上保险: </span>{{ info.motherInsure || info.motherInsure === '1' || info.motherInsure === 1 ? '是' : '否' }}</div>
          </el-col>
        </el-row>
        <el-row
          :gutter="10"
          class="info-content"
        >
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲姓名: </span>{{ info.fatherName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲年龄: </span>{{ info.fatherAge }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲证件类型: </span>{{ info.fatherType }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲证件号码: </span>{{ info.fatherNum }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲联系方式: </span>{{ info.fatherMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲出生日期: </span>{{ info.fatherBirthday }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲民族: </span>{{ info.fatherClan }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲籍贯: </span>{{ info.fatherPlace }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲是否上保险: </span>{{ info.fatherInsure || info.fatherInsure === '1' || info.fatherInsure === 1 ? '是' : '否' }}</div>
          </el-col>
        </el-row>
      </el-form>
    </div>

   
 
  </div>
  <div class="datasave">
  <el-row> 
    <el-col>
      <div style="margin-bottom: 20px;">组学数据存储：</div>
      <file-upload v-model="form.groupData" :pCode="pCode" fileType="7" />
    </el-col>
  </el-row>
 </div>
 <div class="go-back-view">
      <el-button
        type="primary"
        plain
        @click="goBack"
      >返回</el-button>
     
    </div>
  </div>
 
</template>

<script>
import { updateClinicalinformation, getServiceCSData } from "@/api/caseManagement";

import FileUpload from "@/components/FileUpload";

export default {
  props: {
    patientData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    patientTypeMap: {
      type: Object,
      default: () => {
        return {}
      }
    },
    uploadType: {
      type: Array,
      default: () => {
        return []
      }
    },
    routerType: {
      type: String,
      default: ''
    }
  },
  components: {
    FileUpload
  },
  data() {
    return {
      info: {},
    
    }
  },
  computed: {
   
  },
  watch: {
    patientData: {
      handler(newValue) {
        if (Object.keys(newValue).length > 0) {
       
          this.info = JSON.parse(JSON.stringify(newValue))
       
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
  
    goBack() {
      const obj = { path: "/genomicsDataManagement" };
      this.$tab.closeOpenPage(obj);
    }
  }
}
</script>
