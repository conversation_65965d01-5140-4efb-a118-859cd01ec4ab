<template>
  <div class="case-list-info">
    <!-- 孕妇 -->
    <div class="clinical-info">
      <div class="clinical-info-title">患者信息</div>
      <el-form
        ref="infoform"
        :model="info"
        label-width="120px"
        style="width: 100%;"
      >
        <el-row
          :gutter="10"
          class="info-content"
        >
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">姓名: </span>{{ info.gname || info.gName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">性别: </span>{{ info.gender }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">年龄: </span>{{ info.gage || info.gAge }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">患者类型: </span>{{ patientTypeMap[info.patientType] }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">出生日期: </span>{{ info.gbirthday || info.gBirthday }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">民族: </span>{{ info.gclan || info.gClan }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">籍贯: </span>{{ info.gplace || info.gPlace }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">孕周: </span>{{ info.gweek || info.gWeek }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">孕天: </span>{{ info.gday || info.gDay }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">末次月经时间: </span>{{ info.menstruationEndTime }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">月经周期: </span>{{ info.menstrualCycle }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">身高: </span>{{ info.gheight || info.gHeight }}cm</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">体重: </span>{{ info.gweight || info.gWeight }}kg</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">证件类型: </span>{{ info.docType }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">证件号码: </span>{{ info.idNum }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">联系方式: </span>{{ info.gmobile || info.gMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">是否上保险: </span>{{ info.ginsure || info.ginsure === '1' || info.ginsure === 1 || info.gInsure || info.gInsure === '1' || info.gInsure === 1 ? '是' : '否' }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">家属名称: </span>{{ info.familyName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">家属联系方式: </span>{{ info.familyMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">紧急联系人: </span>{{ info.emergencyName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">紧急联系人手机号: </span>{{ info.emergencyMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">地址: </span>{{ info.addressDetails }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">医院名称: </span>{{ info.hospName }}</div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="go-back-view">
      <el-button
        type="primary"
        plain
        @click="goBack"
      >返回</el-button>
    </div>
  </div>
</template>

<script>
import { updateGravidainfo } from "@/api/caseManagement";

import FileUpload from "@/components/FileUpload";

export default {
  props: {
    patientData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    patientTypeMap: {
      type: Object,
      default: () => {
        return {}
      }
    },
    uploadType: {
      type: Array,
      default: () => {
        return []
      }
    },
    routerType: {
      type: String,
      default: ''
    }
  },
  components: {
    FileUpload
  },
  data() {
    return {
      info: {},
      form: {
   
      },
    
     
    
   
      // screeningTypeOption: [
      //   { value: '1', label: '孕早期' },
      //   { value: '2', label: '中期' },
      //   { value: '3', label: '晚期' },
      // ]
    }
  },
  computed: {
    detailDisable() {
      return this.routerType !== 'supplement'
    }
  },
  watch: {
    patientData: {
      handler(newValue) {
        this.info = JSON.parse(JSON.stringify(newValue))
 
     
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
  
 
    goBack() {
      const obj = { path: "/caseManagement/case_list" };
      this.$tab.closeOpenPage(obj);
    }
  }
}
</script>

