<template>
  <div class="case-list-info">
    <!-- 孕妇 -->
    <div class="clinical-info">
      <div class="clinical-info-title">患者信息</div>
      <el-form
        ref="infoform"
        :model="info"
        label-width="120px"
        style="width: 100%;"
      >
        <el-row
          :gutter="10"
          class="info-content"
        >
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">姓名: </span>{{ info.gname || info.gName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">性别: </span>{{ info.gender }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">年龄: </span>{{ info.gage || info.gAge }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">患者类型: </span>{{ patientTypeMap[info.patientType] }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">出生日期: </span>{{ info.gbirthday || info.gBirthday }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">民族: </span>{{ info.gclan || info.gClan }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">籍贯: </span>{{ info.gplace || info.gPlace }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">孕周: </span>{{ info.gweek || info.gWeek }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">孕天: </span>{{ info.gday || info.gDay }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">末次月经时间: </span>{{ info.menstruationEndTime }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">月经周期: </span>{{ info.menstrualCycle }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">身高: </span>{{ info.gheight || info.gHeight }}cm</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">体重: </span>{{ info.gweight || info.gWeight }}kg</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">证件类型: </span>{{ info.docType }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">证件号码: </span>{{ info.idNum }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">联系方式: </span>{{ info.gmobile || info.gMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">是否上保险: </span>{{ info.ginsure || info.ginsure === '1' || info.ginsure === 1 || info.gInsure || info.gInsure === '1' || info.gInsure === 1 ? '是' : '否' }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">家属名称: </span>{{ info.familyName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">家属联系方式: </span>{{ info.familyMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">紧急联系人: </span>{{ info.emergencyName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">紧急联系人手机号: </span>{{ info.emergencyMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">地址: </span>{{ info.addressDetails }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">医院名称: </span>{{ info.hospName }}</div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="clinical-info">
      <div class="clinical-info-title">临床信息</div>
      <el-form
        ref="form"
        :model="form"
        :rules="formRules"
        label-width="150px"
        style="width: 100%;"
      >
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="病历号/就诊卡号：">{{ info.pcode }}</el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-radio-group v-model="form.asStatus" :disabled="detailDisable" class="ml20 mr20 shrink">
              <el-radio :label="false" class="custom-form-item">未见异常</el-radio>
              <el-radio :label="true" class="custom-form-item">胎儿结构异常</el-radio>
            </el-radio-group>
            <el-form-item
              v-if="form.asStatus"
              label="异常说明："
              prop="structureText"
              class="flex"
            >
              <el-input v-model="form.structureText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="12"
            class="display-flex"
          >
            <el-checkbox
              v-model="form.asIndicatorRisk"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >软指标高风险</el-checkbox>
            <el-form-item
              v-if="form.asIndicatorRisk"
              prop="indicatorRiskText"
              class="flex form-content-notitle"
            >
              <el-input v-model="form.indicatorRiskText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="12"
            class="display-flex"
          >
            <el-checkbox
              v-model="form.asSurgicalTreatment"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >介入性手术治疗</el-checkbox>
            <el-form-item
              v-if="form.asSurgicalTreatment"
              prop="surgicalTreatmentText"
              class="flex form-content-notitle"
            >
              <el-input v-model="form.surgicalTreatmentText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="clinical-info">
      <div class="clinical-info-title">NIPT</div>
      <el-form
        ref="formNIPT"
        :model="formNIPT"
        :rules="formNIPTRules"
        label-width="120px"
        style="width: 100%;"
      >
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="采样时间：">
              <el-date-picker
                clearable
                v-model="formNIPT.caiTime"
                type="datetime"
                :disabled="detailDisable"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收样时间：">
              <el-date-picker
                clearable
                v-model="formNIPT.shouTime"
                type="datetime"
                :disabled="detailDisable"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="送检材料：">
              <el-input v-model="formNIPT.songText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="病历门诊号：">
              <el-input v-model="formNIPT.pcode" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="孕次数：">
              <el-input v-model="formNIPT.yunNum" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产次数：">
              <el-input v-model="formNIPT.chanNum" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.asBuliangyun"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >不良孕产史</el-checkbox>
            <el-form-item
              v-if="formNIPT.asBuliangyun"
              label="不良孕产史说明："
              prop="buliangyunText"
              label-width="140px"
              class="flex"
            >
              <el-input v-model="formNIPT.buliangyunText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.asJiazu"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >家族遗传病</el-checkbox>
            <el-form-item
              v-if="formNIPT.asJiazu"
              label="家族遗传病说明："
              prop="jiazuText"
              label-width="140px"
              class="flex"
            >
              <el-input v-model="formNIPT.jiazuText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.asFuqiranseti"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >夫妻双方是否行染色体</el-checkbox>
            <el-form-item
              v-if="formNIPT.asFuqiranseti"
              label="夫妻双方染色体说明："
              prop="fuqiransetiText"
              label-width="170px"
              class="flex"
            >
              <el-input v-model="formNIPT.fuqiransetiText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-form-item label="妊娠情况：">
              <el-radio-group
                v-model="formNIPT.asPregnancy"
                :disabled="detailDisable"
                class="mr20"
              >
                <el-radio :label="false">单胎</el-radio>
                <el-radio :label="true">双胎</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="妊娠情况说明："
              prop="pregnancyText"
              class="flex"
            >
              <el-input v-model="formNIPT.pregnancyText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.asIvf"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >试管婴儿（IVF）</el-checkbox>
            <div v-if="formNIPT.asIvf" class="flex">
              <el-form-item
                label="试管婴儿说明："
                prop="ivfText"
                label-width="130px"
                class="flex"
              >
                <el-input v-model="formNIPT.ivfText" :disabled="detailDisable"></el-input>
              </el-form-item>
              <el-form-item
                label="试管植入日期："
                prop="ivfTime"
                label-width="130px"
                class="flex"
              >
                <el-date-picker
                  clearable
                  v-model="formNIPT.ivfTime"
                  type="datetime"
                  :disabled="detailDisable"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="请选择时间"
                >
                </el-date-picker>
              </el-form-item>
            </div>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.asCaosheng"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >超声检查</el-checkbox>
            <el-form-item
              v-if="formNIPT.asCaosheng"
              label="超声检查说明："
              prop="caoshengText"
              label-width="130px"
              class="flex"
            >
              <el-input v-model="formNIPT.caoshengText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.asXueyeshaicha"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >血清筛查</el-checkbox>
            <el-form-item
              v-if="formNIPT.asXueyeshaicha"
              label="血清筛查说明："
              prop="xueyeshaichaText"
              label-width="130px"
              class="flex"
            >
              <el-input v-model="formNIPT.xueyeshaichaText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="筛查模式：">
              <el-radio-group
                v-model="formNIPT.shaichamoshi"
                :disabled="detailDisable"
                class="display-flex ml20"
              >
                <el-radio label="1" class="lh36">孕早期</el-radio>
                <el-radio label="2" class="lh36">中期</el-radio>
                <el-radio label="3" class="lh36">晚期</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.assanti21"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >21-三体风险值</el-checkbox>
            <el-form-item
              v-if="formNIPT.assanti21"
              label="21-三体风险值说明："
              prop="santiA"
              label-width="160px"
              class="flex"
            >
              <el-input v-model="formNIPT.santiA" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.assanti18"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >18-三体风险值</el-checkbox>
            <el-form-item
              v-if="formNIPT.assanti18"
              label="18-三体风险值说明："
              prop="santiB"
              label-width="160px"
              class="flex"
            >
              <el-input v-model="formNIPT.santiB" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.assanti13"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >13-三体风险值</el-checkbox>
            <el-form-item
              v-if="formNIPT.assanti13"
              label="13-三体风险值说明："
              prop="santiC"
              label-width="160px"
              class="flex"
            >
              <el-input v-model="formNIPT.santiC" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.asYuyuechaungcishoushu"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >预约穿刺手术</el-checkbox>
            <el-form-item
              v-if="formNIPT.asYuyuechaungcishoushu"
              label="预约日期："
              prop="yuyuechaungcishoushuTime"
              class="flex"
            >
              <el-date-picker
                clearable
                v-model="formNIPT.yuyuechaungcishoushuTime"
                type="datetime"
                :disabled="detailDisable"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.asXibaoziliao"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >细胞治疗</el-checkbox>
            <el-form-item
              v-if="formNIPT.asXibaoziliao"
              label="细胞治疗说明："
              prop="xibaoziliaoText"
              label-width="130px"
              class="flex"
            >
              <el-input v-model="formNIPT.xibaoziliaoText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.asZhongliu"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >肿瘤患者</el-checkbox>
            <el-form-item
              v-if="formNIPT.asZhongliu"
              label="肿瘤患者说明："
              prop="zhongliuText"
              label-width="130px"
              class="flex"
            >
              <el-input v-model="formNIPT.zhongliuText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.asYichangshuxue"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >异常输血</el-checkbox>
            <el-form-item
              v-if="formNIPT.asYichangshuxue"
              label="异常输血说明："
              prop="yichangshuxueText"
              label-width="130px"
              class="flex"
            >
              <el-input v-model="formNIPT.yichangshuxueText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.asYizhishoushu"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >移植手术</el-checkbox>
            <el-form-item
              v-if="formNIPT.asYizhishoushu"
              label="移植手术说明："
              prop="yizhishoushuText"
              label-width="130px"
              class="flex"
            >
              <el-input v-model="formNIPT.yizhishoushuText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formNIPT.asMianyizhiliao"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >免疫治疗</el-checkbox>
            <el-form-item
              v-if="formNIPT.asMianyizhiliao"
              label="免疫治疗说明："
              prop="mianyizhiliaoText"
              label-width="130px"
              class="flex"
            >
              <el-input v-model="formNIPT.mianyizhiliaoText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="特殊备注：">
              <el-input v-model="formNIPT.teshubeizhu" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="clinical-info">
      <div class="clinical-info-title">CNV</div>
      <el-form
        ref="formCNV"
        :model="formCNV"
        :rules="formCNVRules"
        label-width="120px"
        style="width: 100%;"
      >
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="采样时间：">
              <el-date-picker
                clearable
                v-model="formCNV.caiTime"
                type="datetime"
                :disabled="detailDisable"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收样时间：">
              <el-date-picker
                clearable
                v-model="formCNV.shouTime"
                type="datetime"
                :disabled="detailDisable"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="送检材料：">
              <el-input v-model="formCNV.songText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="病历门诊号：">
              <el-input v-model="formCNV.pcode" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="clinical-info-form-blod">产前/流产物</div>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="孕次数：">
              <el-input v-model="formCNV.yunNum" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产次数：">
              <el-input v-model="formCNV.chanNum" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="流产方式：">
              <el-input v-model="formCNV.liuType" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>

          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCNV.asChanqian"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >产前诊断</el-checkbox>
            <el-form-item
              v-if="formCNV.asChanqian"
              label="产前诊断说明："
              prop="chanqianText"
              label-width="130px"
              class="flex"
            >
              <el-input v-model="formCNV.chanqianText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="主诉：">
              <el-input v-model="formCNV.zhusuText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCNV.asBuliang"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >不良孕产史</el-checkbox>
            <el-form-item
              v-if="formCNV.asBuliang"
              label="不良孕产史说明："
              prop="buliangText"
              label-width="140px"
              class="flex"
            >
              <el-input v-model="formCNV.buliangText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCNV.asJiazu"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >家族遗传病</el-checkbox>
            <el-form-item
              v-if="formCNV.asJiazu"
              label="家族遗传病说明："
              prop="jiazuText"
              label-width="140px"
              class="flex"
            >
              <el-input v-model="formCNV.jiazuText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCNV.asFuqi"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >夫妻双方是否行染色体</el-checkbox>
            <el-form-item
              v-if="formCNV.asFuqi"
              label="夫妻双方染色体说明："
              prop="fuqiText"
              label-width="170px"
              class="flex"
            >
              <el-input v-model="formCNV.fuqiText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-form-item label="妊娠情况：">
              <el-radio-group
                v-model="formCNV.asPregnancy"
                :disabled="detailDisable"
                class="mr20"
              >
                <el-radio :label="false">单胎</el-radio>
                <el-radio :label="true">双胎</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="妊娠情况说明："
              prop="pregnancyText"
              class="flex"
            >
              <el-input v-model="formCNV.pregnancyText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCNV.asIvf"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >试管婴儿（IVF）</el-checkbox>
            <div v-if="formCNV.asIvf" class="flex">
              <el-form-item
                label="试管婴儿说明："
                prop="ivfText"
                label-width="130px"
                class="flex"
              >
                <el-input v-model="formCNV.ivfText" :disabled="detailDisable"></el-input>
              </el-form-item>
              <!-- <el-form-item
                label="试管植入日期："
                prop="isIVFtime"
                class="flex"
              >
                <el-date-picker
                  clearable
                  v-model="formCNV.isIVFtime"
                  :disabled="detailDisable"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="请选择时间"
                >
                </el-date-picker>
              </el-form-item> -->
            </div>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCNV.asCaosheng"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >超声检查</el-checkbox>
            <el-form-item
              v-if="formCNV.asCaosheng"
              label="超声检查说明："
              prop="caoshengText"
              label-width="130px"
              class="flex"
            >
              <el-input v-model="formCNV.caoshengText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCNV.asSerum"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >血清筛查</el-checkbox>
            <el-form-item
              v-if="formCNV.asSerum"
              label="血清筛查说明："
              prop="serumText"
              label-width="130px"
              class="flex"
            >
              <el-input v-model="formCNV.serumText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="筛查模式：">
              <el-radio-group
                v-model="formCNV.screeningModel"
                :disabled="detailDisable"
                class="display-flex ml20"
              >
                <el-radio label="1" class="lh36">孕早期</el-radio>
                <el-radio label="2" class="lh36">中期</el-radio>
                <el-radio label="3" class="lh36">晚期</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCNV.assanti21"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >21-三体风险值</el-checkbox>
            <el-form-item
              v-if="formCNV.assanti21"
              label="21-三体风险值说明："
              prop="santiA"
              label-width="160px"
              class="flex"
            >
              <el-input v-model="formCNV.santiA" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCNV.assanti18"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >18-三体风险值</el-checkbox>
            <el-form-item
              v-if="formCNV.assanti18"
              label="18-三体风险值说明："
              prop="santiB"
              label-width="160px"
              class="flex"
            >
              <el-input v-model="formCNV.santiB" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCNV.assanti13"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >13-三体风险值</el-checkbox>
            <el-form-item
              v-if="formCNV.assanti13"
              label="13-三体风险值说明："
              prop="santiC"
              label-width="160px"
              class="flex"
            >
              <el-input v-model="formCNV.santiC" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="clinical-info-form-blod">患者</div>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="主诉：">
              <el-input v-model="formCNV.hzhusuText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="特殊备注：">
              <el-input v-model="formCNV.hteshubeizhu" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-checkbox
              v-model="formCNV.asZhili"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >智力障碍</el-checkbox>
            <el-checkbox
              v-model="formCNV.asXinzangbing"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >先天性心脏病</el-checkbox>
            <el-checkbox
              v-model="formCNV.asFayuchihuan"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >发育迟缓</el-checkbox>
            <el-checkbox
              v-model="formCNV.asJiwuzhu"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >肌无力</el-checkbox>
            <el-checkbox
              v-model="formCNV.asZhitijixing"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >肢体畸形</el-checkbox>
            <el-checkbox
              v-model="formCNV.asZangqijixing"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >脏器畸形</el-checkbox>
            <el-checkbox
              v-model="formCNV.asShengzhiqijixing"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >生殖系统畸形</el-checkbox>
            <el-checkbox
              v-model="formCNV.asZibi"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >自闭症</el-checkbox>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="formCNV.asOthen"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >其他</el-checkbox>
            <el-form-item
              v-if="formCNV.asOthen"
              label="其他疾病："
              prop="othenText"
              class="flex"
            >
              <el-input v-model="formCNV.othenText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="其他诊断：">
              <el-input v-model="formCNV.othenDiagnosis" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="诊断项目：">
              <el-input v-model="formCNV.othenDiagnosisPerject" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="诊断结果：">
              <el-input v-model="formCNV.othenDiagnosisEnd" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="其他临床信息说明：" label-width="150px">
              <el-input v-model="formCNV.othenDiagnosisInfo" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="16">
            <el-form-item label="知情同意书上传：" label-width="140px">
              <file-upload v-model="formCNV.fileUsr" :fileType="uploadType" :disabled="detailDisable" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="go-back-view">
      <el-button
        type="primary"
        plain
        @click="goBack"
      >返回</el-button>
      <el-button
        v-if="!detailDisable"
        type="primary"
        @click="submitForm"
      >保存</el-button>
    </div>
  </div>
</template>

<script>
import { updateGravidainfo } from "@/api/caseManagement";

import FileUpload from "@/components/FileUpload";

export default {
  props: {
    patientData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    patientTypeMap: {
      type: Object,
      default: () => {
        return {}
      }
    },
    uploadType: {
      type: Array,
      default: () => {
        return []
      }
    },
    routerType: {
      type: String,
      default: ''
    }
  },
  components: {
    FileUpload
  },
  data() {
    const structureTextVal = (rule, value, callback) => {
      if (this.form.asStatus && value === '') {
        callback(new Error("请填写异常说明"));
      } else {
        callback();
      }
    };
    const indicatorRiskTextVal = (rule, value, callback) => {
      if (this.form.asIndicatorRisk && value === '') {
        callback(new Error("请填写软指标高风险信息"));
      } else {
        callback();
      }
    };
    const surgicalTreatmentTextVal = (rule, value, callback) => {
      if (this.form.asSurgicalTreatment && value === '') {
        callback(new Error("请填写介入性手术治疗信息"));
      } else {
        callback();
      }
    };

    const buliangyunTextVal = (rule, value, callback) => {
      if (this.formNIPT.asBuliangyun && value === '') {
        callback(new Error("请填写不良孕产史说明"));
      } else {
        callback();
      }
    };
    const jiazuTextVal = (rule, value, callback) => {
      if (this.formNIPT.asJiazu && value === '') {
        callback(new Error("请填写家族遗传病说明"));
      } else {
        callback();
      }
    };
    const fuqiransetiTextVal = (rule, value, callback) => {
      if (this.formNIPT.asFuqiranseti && value === '') {
        callback(new Error("请填写夫妻双方染色体说明"));
      } else {
        callback();
      }
    };
    const ivfTextNIPTVal = (rule, value, callback) => {
      if (this.formNIPT.asIvf && value === '') {
        callback(new Error("请填写试管婴儿说明"));
      } else {
        callback();
      }
    };
    const ivfTimeNIPTVal = (rule, value, callback) => {
      if (this.formNIPT.asIvf && value === '') {
        callback(new Error("请选择试管植入日期"));
      } else {
        callback();
      }
    };
    const caoshengTextNIPTVal = (rule, value, callback) => {
      if (this.formNIPT.asCaosheng && value === '') {
        callback(new Error("请填写超声检查说明"));
      } else {
        callback();
      }
    };
    const xueyeshaichaTextNIPTVal = (rule, value, callback) => {
      if (this.formNIPT.asXueyeshaicha && value === '') {
        callback(new Error("请填写血清筛查说明"));
      } else {
        callback();
      }
    };
    const santiANIPTVal = (rule, value, callback) => {
      if (this.formNIPT.assanti21 && value === '') {
        callback(new Error("请填写21-三体风险值说明"));
      } else {
        callback();
      }
    };
    const santiBNIPTVal = (rule, value, callback) => {
      if (this.formNIPT.assanti18 && value === '') {
        callback(new Error("请填写18-三体风险值说明"));
      } else {
        callback();
      }
    };
    const santiCNIPTVal = (rule, value, callback) => {
      if (this.formNIPT.assanti13 && value === '') {
        callback(new Error("请填写13-三体风险值说明"));
      } else {
        callback();
      }
    };
    const yuyuechaungcishoushuTimeNIPTVal = (rule, value, callback) => {
      if (this.formNIPT.asYuyuechaungcishoushu && value === '') {
        callback(new Error("请选择预约日期"));
      } else {
        callback();
      }
    };
    const xibaoziliaoTextNIPTVal = (rule, value, callback) => {
      if (this.formNIPT.asXibaoziliao && value === '') {
        callback(new Error("请填写细胞治疗说明"));
      } else {
        callback();
      }
    };
    const zhongliuTextNIPTVal = (rule, value, callback) => {
      if (this.formNIPT.asZhongliu && value === '') {
        callback(new Error("请填写肿瘤患者说明"));
      } else {
        callback();
      }
    };
    const yichangshuxueTextNIPTVal = (rule, value, callback) => {
      if (this.formNIPT.asYichangshuxue && value === '') {
        callback(new Error("请填写异常输血说明"));
      } else {
        callback();
      }
    };
    const yizhishoushuTextNIPTVal = (rule, value, callback) => {
      if (this.formNIPT.asYizhishoushu && value === '') {
        callback(new Error("请填写移植手术说明"));
      } else {
        callback();
      }
    };
    const mianyizhiliaoTextNIPTVal = (rule, value, callback) => {
      if (this.formNIPT.asMianyizhiliao && value === '') {
        callback(new Error("请填写免疫治疗说明"));
      } else {
        callback();
      }
    };

    const chanqianTextCNVVal = (rule, value, callback) => {
      if (this.formCNV.asChanqian && value === '') {
        callback(new Error("请填写产前诊断说明"));
      } else {
        callback();
      }
    };
    const buliangTextCNVVal = (rule, value, callback) => {
      if (this.formCNV.asBuliang && value === '') {
        callback(new Error("请填写不良孕产史说明"));
      } else {
        callback();
      }
    };
    const jiazuTextCNVVal = (rule, value, callback) => {
      if (this.formCNV.asJiazu && value === '') {
        callback(new Error("请填写家族遗传病说明"));
      } else {
        callback();
      }
    };
    const fuqiTextCNVVal = (rule, value, callback) => {
      if (this.formCNV.asFuqi && value === '') {
        callback(new Error("请填写夫妻双方染色体说明"));
      } else {
        callback();
      }
    };
    const ivfTextCNVVal = (rule, value, callback) => {
      if (this.formCNV.asIvf && value === '') {
        callback(new Error("请填写试管婴儿说明"));
      } else {
        callback();
      }
    };
    // const isIVFtimeCNVVal = (rule, value, callback) => {
    //   if (this.formCNV.asIvf && value === '') {
    //     callback(new Error("请选择试管植入日期"));
    //   } else {
    //     callback();
    //   }
    // };
    const caoshengTextCNVVal = (rule, value, callback) => {
      if (this.formCNV.asCaosheng && value === '') {
        callback(new Error("请填写超声检查说明"));
      } else {
        callback();
      }
    };
    const serumTextCNVVal = (rule, value, callback) => {
      if (this.formCNV.asSerum && value === '') {
        callback(new Error("请填写血清筛查说明"));
      } else {
        callback();
      }
    };
    const santiACNVVal = (rule, value, callback) => {
      if (this.formCNV.assanti21 && value === '') {
        callback(new Error("请填写21-三体风险值说明"));
      } else {
        callback();
      }
    };
    const santiBCNVVal = (rule, value, callback) => {
      if (this.formCNV.assanti18 && value === '') {
        callback(new Error("请填写18-三体风险值说明"));
      } else {
        callback();
      }
    };
    const santiCCNVVal = (rule, value, callback) => {
      if (this.formCNV.assanti13 && value === '') {
        callback(new Error("请填写13-三体风险值说明"));
      } else {
        callback();
      }
    };
    const othenTextCNVVal = (rule, value, callback) => {
      if (this.formCNV.asOthen && value === '') {
        callback(new Error("请填写疾病信息"));
      } else {
        callback();
      }
    };
    return {
      info: {},
      form: {
        id: '',
        userId: '',
        patientId: '',
        pcode: '',
        asIndicatorRisk: false,
        indicatorRiskText: '',
        asSurgicalTreatment: false,
        surgicalTreatmentText: '',
        asStatus: '',
        structureText: '',
      },
      formRules: {
        structureText: [
          { required: true, validator: structureTextVal, trigger: "change" }
        ],
        indicatorRiskText: [
          { required: true, validator: indicatorRiskTextVal, trigger: "change" }
        ],
        surgicalTreatmentText: [
          { required: true, validator: surgicalTreatmentTextVal, trigger: "change" }
        ],
      },
      formNIPT: {
        id: '',
        userId: '',
        patientId: '',
        caiTime: '',
        shouTime: '',
        songText: '',
        pcode: '',
        yunNum:'',
        chanNum: '',
        asBuliangyun: false,
        buliangyunText: '',
        asJiazu: false,
        jiazuText: '',
        asFuqiranseti: false,
        fuqiransetiText: '',
        asPregnancy: '',
        pregnancyText: '',
        asIvf: false,
        ivfText: '',
        ivfTime: '',
        asCaosheng: false,
        caoshengText: '',
        asXueyeshaicha: false,
        xueyeshaichaText: '',
        shaichamoshi: '',
        santiType: '', // 三体 总的， '21,18,13' , 保存和 获取 都需要单独处理
        assanti21: false,
        santiA: '',
        assanti18: false,
        santiB: '',
        assanti13: false,
        santiC: '',
        asYuyuechaungcishoushu: false,
        yuyuechaungcishoushuTime: '',
        asXibaoziliao: false,
        xibaoziliaoText: '',
        asZhongliu: false,
        zhongliuText: '',
        asYichangshuxue: false,
        yichangshuxueText: '',
        asYizhishoushu: false,
        yizhishoushuText: '',
        asMianyizhiliao: false,
        mianyizhiliaoText: '',
        teshubeizhu: '',
      },
      formNIPTRules: {
        buliangyunText: [
          { required: true, validator: buliangyunTextVal, trigger: "change" }
        ],
        jiazuText: [
          { required: true, validator: jiazuTextVal, trigger: "change" }
        ],
        fuqiransetiText: [
          { required: true, validator: fuqiransetiTextVal, trigger: "change" }
        ],
        ivfText: [
          { required: true, validator: ivfTextNIPTVal, trigger: "change" }
        ],
        ivfTime: [
          { required: true, validator: ivfTimeNIPTVal, trigger: "change" }
        ],
        caoshengText: [
          { required: true, validator: caoshengTextNIPTVal, trigger: "change" }
        ],
        xueyeshaichaText: [
          { required: true, validator: xueyeshaichaTextNIPTVal, trigger: "change" }
        ],
        santiA: [
          { required: true, validator: santiANIPTVal, trigger: "change" }
        ],
        santiB: [
          { required: true, validator: santiBNIPTVal, trigger: "change" }
        ],
        santiC: [
          { required: true, validator: santiCNIPTVal, trigger: "change" }
        ],
        yuyuechaungcishoushuTime: [
          { required: true, validator: yuyuechaungcishoushuTimeNIPTVal, trigger: "change" }
        ],
        xibaoziliaoText: [
          { required: true, validator: xibaoziliaoTextNIPTVal, trigger: "change" }
        ],
        zhongliuText: [
          { required: true, validator: zhongliuTextNIPTVal, trigger: "change" }
        ],
        yichangshuxueText: [
          { required: true, validator: yichangshuxueTextNIPTVal, trigger: "change" }
        ],
        yizhishoushuText: [
          { required: true, validator: yizhishoushuTextNIPTVal, trigger: "change" }
        ],
        mianyizhiliaoText: [
          { required: true, validator: mianyizhiliaoTextNIPTVal, trigger: "change" }
        ],
      },
      formCNV: {
        id: '',
        userId: '',
        patientId: '',
        caiTime: '',
        shouTime: '',
        songText: '',
        pcode: '',
        yunNum: '',
        chanNum: '',
        liuType: '',
        asChanqian: '',
        chanqianText: '',
        zhusuText: '',
        asBuliang: false,
        buliangText: '',
        asJiazu: false,
        jiazuText: '',
        asFuqi: false,
        fuqiText: '',
        asPregnancy: '',
        pregnancyText: '',
        asIvf: false,
        ivfText: '',
        // isIVFtime: '',
        asCaosheng: false,
        caoshengText: '',
        asSerum: false,
        serumText: '',
        screeningModel: 0,
        santiType: '', // 三体 总的， '21,18,13' , 保存和 获取 都需要单独处理
        assanti21: false,
        santiA: '',
        assanti18: false,
        santiB: '',
        assanti13: false,
        santiC: '',
        hzhusuText: '',
        hteshubeizhu: '',
        asZhili: false,
        asXinzangbing: false,
        asFayuchihuan: false,
        asJiwuzhu: false,
        asZhitijixing: false,
        asZangqijixing: false,
        asShengzhiqijixing: false,
        asZibi: false,
        asOthen: false,
        othenText: '',
        othenDiagnosis: '',
        othenDiagnosisPerject: '',
        othenDiagnosisEnd: '',
        othenDiagnosisInfo: '',
        fileUsr: '',
      },
      formCNVRules: {
        chanqianText: [
          { required: true, validator: chanqianTextCNVVal, trigger: "change" }
        ],
        buliangText: [
          { required: true, validator: buliangTextCNVVal, trigger: "change" }
        ],
        jiazuText: [
          { required: true, validator: jiazuTextCNVVal, trigger: "change" }
        ],
        fuqiText: [
          { required: true, validator: fuqiTextCNVVal, trigger: "change" }
        ],
        ivfText: [
          { required: true, validator: ivfTextCNVVal, trigger: "change" }
        ],
        // isIVFtime: [
        //   { required: true, validator: isIVFtimeCNVVal, trigger: "change" }
        // ],
        caoshengText: [
          { required: true, validator: caoshengTextCNVVal, trigger: "change" }
        ],
        serumText: [
          { required: true, validator: serumTextCNVVal, trigger: "change" }
        ],
        santiA: [
          { required: true, validator: santiACNVVal, trigger: "change" }
        ],
        santiB: [
          { required: true, validator: santiBCNVVal, trigger: "change" }
        ],
        santiC: [
          { required: true, validator: santiCCNVVal, trigger: "change" }
        ],
        othenText: [
          { required: true, validator: othenTextCNVVal, trigger: "change" }
        ],
      },
      // screeningTypeOption: [
      //   { value: '1', label: '孕早期' },
      //   { value: '2', label: '中期' },
      //   { value: '3', label: '晚期' },
      // ]
    }
  },
  computed: {
    detailDisable() {
      return this.routerType !== 'supplement'
    }
  },
  watch: {
    patientData: {
      handler(newValue) {
        this.info = JSON.parse(JSON.stringify(newValue))
        const formInfo = newValue.gravidainfoVOList ? newValue.gravidainfoVOList[0] : {}
        const formNIPTInfo = newValue.gravidaniptVOList ? newValue.gravidaniptVOList[0] : {}
        const formCNVInfo = newValue.gravidacnvVOList ? newValue.gravidacnvVOList[0] : {}
        const santiNIPTData = {
          assanti21: false,
          assanti18: false,
          assanti13: false,
        }
        const santiCNVData = {
          assanti21: false,
          assanti18: false,
          assanti13: false,
        }
        if (Object.keys(formNIPTInfo).length > 0) {
          if (!formNIPTInfo.santiType) {
            formNIPTInfo.santiType = ''
          }
          if (formNIPTInfo.santiType.length > 0) {
            const arr = formNIPTInfo.santiType.split(',')
            arr.forEach(ele => {
              santiNIPTData[`assanti${ele}`] = true
            })
          }
        }
        if (Object.keys(formCNVInfo).length > 0) {
          if (!formCNVInfo.santiType) {
            formCNVInfo.santiType = ''
          }
          if (formCNVInfo.santiType.length > 0) {
            const arr = formCNVInfo.santiType.split(',')
            arr.forEach(ele => {
              santiCNVData[`assanti${ele}`] = true
            })
          }
        }
        this.form = { ...this.form, ...formInfo, ...{
          patientId: newValue.id,
          userId: newValue.userId,
        }}
        this.formNIPT = { ...this.formNIPT, ...formNIPTInfo, ...santiNIPTData, ...{
          patientId: newValue.id,
          userId: newValue.userId,
        }}
        this.formCNV = { ...this.formCNV, ...formCNVInfo, ...santiCNVData, ...{
          patientId: newValue.id,
          userId: newValue.userId,
        }}
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    submitForm() {
      Promise.all([this.formValidate('form'), this.formValidate('formNIPT'), this.formValidate('formCNV')]).then(() => {
        let santiTypeNIPTArr = []
        let santiTypeCNVArr = []
        const santiArr = [21, 18, 13]
        santiArr.forEach(ele => {
          if (this.formNIPT[`assanti${ele}`]) {
            santiTypeNIPTArr.push(ele)
          }
          if (this.formCNV[`assanti${ele}`]) {
            santiTypeCNVArr.push(ele)
          }
        })
        const params = {
          patientId: this.patientData.id,
          pcode: this.patientData.pcode,
          userId: this.patientData.userId,
          gravidainfoDTO: this.form,
          gravidaniptDTO: { ...this.formNIPT, ...{
            santiType: santiTypeNIPTArr.join()
          }},
          gravidacnvDTO: { ...this.formCNV, ...{
            santiType: santiTypeCNVArr.join()
          }}
        }
        updateGravidainfo(params).then(res => {
          this.$modal.msgSuccess("修改成功");
          setTimeout(() => {
            this.goBack()
          }, 1000);
        })
      }).catch(() => {
        this.$modal.msgError("请完善病历信息");
      })
    },
    formValidate(formName) {
      return new Promise((resolve, reject) => {
        this.$refs[formName].validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        });
      })
    },
    goBack() {
      const obj = { path: "/caseManagement/case_list" };
      this.$tab.closeOpenPage(obj);
    }
  }
}
</script>
