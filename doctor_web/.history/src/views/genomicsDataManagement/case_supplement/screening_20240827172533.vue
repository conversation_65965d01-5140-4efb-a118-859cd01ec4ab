<template>
  <div class="case-list-info">
    <!-- 携带者 -->
    <div class="clinical-info">
      <div class="clinical-info-title">患者信息</div>
      <el-form
        ref="infoform"
        :model="info"
        label-width="120px"
        style="width: 100%;"
      >
        <el-row
          :gutter="10"
          class="info-content"
        >
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲姓名: </span>{{ info.motherName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲年龄: </span>{{ info.motherAge }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲证件类型: </span>{{ info.motherType }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲证件号码: </span>{{ info.motherNum }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">患者类型: </span>{{ patientTypeMap[info.patientType] }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲联系方式: </span>{{ info.motherMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲出生日期: </span>{{ info.motherBirthday }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲民族: </span>{{ info.motherClan }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲籍贯: </span>{{ info.motherPlace }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">母亲是否上保险: </span>{{ info.motherInsure || info.motherInsure === '1' || info.motherInsure === 1 ? '是' : '否' }}</div>
          </el-col>
        </el-row>
        <el-row
          :gutter="10"
          class="info-content"
        >
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲姓名: </span>{{ info.fatherName }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲年龄: </span>{{ info.fatherAge }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲证件类型: </span>{{ info.fatherType }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲证件号码: </span>{{ info.fatherNum }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲联系方式: </span>{{ info.fatherMobile }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲出生日期: </span>{{ info.fatherBirthday }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲民族: </span>{{ info.fatherClan }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲籍贯: </span>{{ info.fatherPlace }}</div>
          </el-col>
          <el-col :span="6">
            <div class="info-item-view"><span class="item-title">父亲是否上保险: </span>{{ info.fatherInsure || info.fatherInsure === '1' || info.fatherInsure === 1 ? '是' : '否' }}</div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="clinical-info">
      <div class="clinical-info-title">临床信息</div>
      <el-form
        ref="form"
        :model="form"
        :rules="formRules"
        label-width="120px"
        style="width: 100%;"
      >
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="采样时间：">
              <el-date-picker
                clearable
                v-model="form.sampleStart"
                type="datetime"
                :disabled="detailDisable"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收样时间：">
              <el-date-picker
                clearable
                v-model="form.sampleEnd"
                type="datetime"
                :disabled="detailDisable"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="送检材料：">
              <el-input v-model="form.inspectionMaterials" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="送检科室：">
              <el-input v-model="form.inspectionDepartment" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="送检医师：">
              <el-input v-model="form.inspectionDoctor" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :span="24" class="display-flex">
            <div class="custom-form-title">送检原因：</div>
            <div class="flex">
              <div class="display-flex">
                <el-radio-group
                  v-model="form.rHealthType"
                  :disabled="detailDisable"
                  class="display-flex"
                >
                  <el-radio :label="false" class="custom-form-item">普通健康人群</el-radio>
                  <el-radio :label="true" class="custom-form-item">近亲结婚</el-radio>
                </el-radio-group>
                <el-form-item
                  v-if="form.rHealthType"
                  prop="rhealthText"
                  label="夫妻亲缘关系："
                  label-width="150px"
                  class="flex ml20"
                >
                  <el-input v-model="form.rhealthText" :disabled="detailDisable"></el-input>
                </el-form-item>
              </div>
              <div class="display-flex">
                <!-- <el-checkbox v-model="form.asrjzqz" class="custom-form-item">家族遗传病史</el-checkbox> -->
                <el-form-item
                  label="家族遗传病史"
                  prop="rjzqz"
                >
                  <el-radio-group
                    v-model="form.rjzqz"
                    :disabled="detailDisable"
                  >
                    <el-radio :label="false">未诊断</el-radio>
                    <el-radio :label="true">已诊断</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  v-if="form.rjzqz"
                  prop="rjzqzText"
                  label="具体描述："
                  class="flex"
                >
                  <el-input v-model="form.rjzqzText" :disabled="detailDisable"></el-input>
                </el-form-item>
              </div>
              <div class="display-flex">
                <el-checkbox v-model="form.rfqHealth" :disabled="detailDisable" class="custom-form-item mr20">夫妻一方为已知致病变异携带者</el-checkbox>
                <el-form-item
                  v-if="form.rfqHealth"
                  label="姓名及检测结果："
                  prop="rfqText"
                  label-width="150px"
                  class="flex"
                >
                  <el-input v-model="form.rfqText" :disabled="detailDisable"></el-input>
                </el-form-item>
              </div>
              <div class="display-flex">
                <el-checkbox v-model="form.asrPoorPregnancy" :disabled="detailDisable" class="custom-form-item">不良孕产史：</el-checkbox>
                <el-form-item
                  v-if="form.asrPoorPregnancy"
                  prop="selectrPoorPregnancy"
                  class="form-content-notitle ml20"
                >
                  <el-checkbox-group v-model="form.selectrPoorPregnancy" :disabled="detailDisable">
                    <el-checkbox label="1">自然流产</el-checkbox>
                    <el-checkbox label="2">结构畸形</el-checkbox>
                    <el-checkbox label="3">子代患染色体病</el-checkbox>
                    <el-checkbox label="4">子代患单基因病</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item
                  v-if="form.asrPoorPregnancy"
                  label="描述："
                  prop="rpoorText"
                  class="flex"
                >
                  <el-input v-model="form.rpoorText" :disabled="detailDisable"></el-input>
                </el-form-item>
              </div>
              <div class="display-flex">
                <el-checkbox v-model="form.rAuxiliary" :disabled="detailDisable" class="custom-form-item">辅助生殖需要</el-checkbox>
                <el-checkbox v-model="form.rDonor" :disabled="detailDisable" class="custom-form-item">供精或供卵</el-checkbox>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-form-item label="目前是否怀孕：">
              <el-select
                v-model="form.asGravida"
                :disabled="detailDisable"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in isPregnantOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <template v-if="form.asGravida">
              <el-col :span="8">
                <el-form-item label="孕周：" prop="asGravidaWeek">
                  <el-input v-model="form.asGravidaWeek" :disabled="detailDisable"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="孕天：" prop="asGravidaDay">
                  <el-input v-model="form.asGravidaDay" :disabled="detailDisable"></el-input>
                </el-form-item>
              </el-col>
            </template>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-form-item label="胎儿有无异常：">
              <el-radio-group
                v-model="form.asBaby"
                :disabled="detailDisable"
                class="mr20"
              >
                <el-radio :label="true">有</el-radio>
                <el-radio :label="false">无</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="form.asBaby"
              label="胎儿异常描述："
              prop="asBabyText"
              :disabled="detailDisable"
              label-width="140px"
              class="flex"
            >
              <el-input v-model="form.asBabyText"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="form.cellTherapy"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >细胞治疗</el-checkbox>
            <el-form-item
              v-if="form.cellTherapy"
              label="细胞治疗说明："
              prop="cellTherapyText"
              label-width="140px"
              class="flex"
            >
              <el-input v-model="form.cellTherapyText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="form.transplant"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >移植手术</el-checkbox>
            <el-form-item
              v-if="form.transplant"
              label="移植手术说明："
              prop="transplantText"
              label-width="140px"
              class="flex"
            >
              <el-input v-model="form.transplantText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-checkbox
              v-model="form.bloodOk"
              :disabled="detailDisable"
              class="custom-form-item ml20 mr20 shrink"
            >异常输血</el-checkbox>
            <el-form-item
              v-if="form.bloodOk"
              label="异常输血说明："
              prop="bloodText"
              label-width="140px"
              class="flex"
            >
              <el-input v-model="form.bloodText" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="特殊备注：">
              <el-input v-model="form.specialRemarks" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            class="display-flex"
          >
            <el-form-item label="配偶是否做过：">
              <el-radio-group v-model="form.spouseHave" :disabled="detailDisable">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <template v-if="form.spouseHave">
              <el-col :span="8">
                <el-form-item label="检测编号：" prop="spouseCode">
                  <el-input v-model="form.spouseCode" :disabled="detailDisable"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="检测结果：" prop="spouseResults">
                  <el-input v-model="form.spouseResults" :disabled="detailDisable"></el-input>
                </el-form-item>
              </el-col>
            </template>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="16">
            <el-form-item label="知情同意书上传：" label-width="140px">
              <file-upload v-model="form.proveUrl" :fileType="uploadType" :disabled="detailDisable" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="16">
            <el-form-item label="同步：">
              <el-radio-group v-model="form.asSync" :disabled="detailDisable">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- <div class="clinical-info-title mt30">检测携带的变异</div>
      <el-table
        :data="variationResult"
        border
      >
        <el-table-column
          label="关系"
          prop="data"
        >
        </el-table-column>
        <el-table-column
          label="基因"
          prop="name"
        >
        </el-table-column>
        <el-table-column
          label="变异位置"
          prop="judgment"
        >
        </el-table-column>
        <el-table-column
          label="基因亚区"
          prop="data"
        >
        </el-table-column>
        <el-table-column
          label="HGVS"
          prop="name"
        >
        </el-table-column>
        <el-table-column
          label="变异类型"
          prop="judgment"
        >
        </el-table-column>
        <el-table-column
          label="杂合性"
          prop="data"
        >
        </el-table-column>
        <el-table-column
          label="变异评级"
          prop="name"
        >
        </el-table-column>
        <el-table-column
          label="疾病及遗传方式"
          prop="judgment"
        >
        </el-table-column>
      </el-table>

      <div class="clinical-info-title mt30">检测结果</div>
      <el-table
        :data="detectionResult"
        border
      >
        <el-table-column
          label="关系"
          prop="data"
        >
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleDownload(scope.row)"
              v-hasPermi="['caseManagement:supplement:download']"
            >下载</el-button>
            <el-button
              size="mini"
              type="text"
              @click="handlePreview(scope.row)"
              v-hasPermi="['caseManagement:supplement:preview']"
            >预览</el-button>
          </template>
        </el-table-column>
      </el-table> -->

    </div>
    <!-- 如果需要展示多个报告， 在报告的右上角有个 下拉，展示所有报告， 点击切换对应的报告内容 -->
    <div v-if="detailDisable" class="clinical-info">
      <div class="clinical-info-title">检测报告</div>
      <el-form
        ref="form"
        :model="testingReportSample"
        label-width="120px"
        style="width: 100%;"
      >
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="样本编号：">
              <div>{{ testingReportSample.caseid }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报告日期：">
              <div>{{ testingReportSample.reportdate }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="检测项目：">
          <div>{{ testingReportSample.testitem }}</div>
        </el-form-item>
        <el-form-item label="检测结论：">
          <div>{{ testingReportSample.check_conclusion }}</div>
        </el-form-item>
      </el-form>
      <div class="clinical-info-title mt30">检测结果</div>
      <el-form
        ref="form"
        :model="testingReportSample"
        label-width="120px"
        style="width: 100%;"
      >
        <el-form-item label="生育-配对结论：" label-width="140px">
          <div>{{ testingReportResult.highrisk_pair_conclusion }}</div>
        </el-form-item>
        <el-form-item label="生育-连锁X结论：" label-width="140px">
          <div>{{ testingReportResult.highrisk_x_conclusion }}</div>
        </el-form-item>
      </el-form>
      <div class="clinical-info-title mt30">SNV数据</div>
      <el-table
        :data="testingReportResult.snv_data"
        border
      >
        <el-table-column
          label="结果分类"
          prop="snvtype"
        >
        </el-table-column>
        <el-table-column
          label="结果子分类"
          prop="sub_snvtype"
        >
        </el-table-column>
        <el-table-column
          label="基因"
          prop="gene"
        >
        </el-table-column>
        <el-table-column
          label="变异位置"
          prop="position"
        >
        </el-table-column>
        <el-table-column
          label="基因亚区"
          prop="exon"
        >
        </el-table-column>
        <el-table-column
          label="HGVS"
          prop="hgvs"
        >
        </el-table-column>
        <el-table-column
          label="变异类型"
          prop="type"
        >
        </el-table-column>
        <el-table-column label="杂合性">
          <template slot-scope="scope">
            <template v-if="scope.row.genotype && scope.row.genotype !== ''">
              <div v-for="item in scope.row.genotype.split(';')" :key="item">{{ item }}</div>
            </template>
          </template>
        </el-table-column>
        <el-table-column
          label="变异评级"
          prop="acmglevel"
        >
        </el-table-column>
        <el-table-column
          label="疾病及遗传方式"
          prop="disease"
        >
        </el-table-column>
      </el-table>
      <div class="clinical-info-title mt30">CNV数据</div>
      <el-table
        :data="testingReportResult.cnv_data"
        border
      >
        <el-table-column
          label="样本基本信息"
          prop="sample"
        >
        </el-table-column>
        <el-table-column
          label="CNV模式"
          prop="cnvmode"
        >
        </el-table-column>
        <el-table-column
          label="结果分类"
          prop="cnvtype"
        >
        </el-table-column>
        <el-table-column
          label="结果子分类"
          prop="sub_cnvtype"
        >
        </el-table-column>
        <el-table-column
          label="基因"
          prop="gene"
        >
        </el-table-column>
        <el-table-column
          label="染色体位置"
          prop="chr_position"
        >
        </el-table-column>
        <el-table-column
          label="染色体区段"
          prop="chr_region"
        >
        </el-table-column>
        <el-table-column
          label="CNV类型"
          prop="type"
        >
        </el-table-column>
        <el-table-column
          label="大小"
          prop="mut_size"
        >
        </el-table-column>
        <el-table-column
          label="变异评级"
          prop="mut_asses"
        >
        </el-table-column>
        <el-table-column label="区段对应疾病">
          <template slot-scope="scope">
            <template v-if="scope.row.disease && scope.row.disease !== ''">
              <div v-for="item in scope.row.disease.split(';')" :key="item">{{ item }}</div>
            </template>
          </template>
        </el-table-column>
        <el-table-column
          label="杂合性"
          prop="genotype"
        >
          <template slot-scope="scope">
            <template v-if="scope.row.genotype && scope.row.genotype !== ''">
              <div v-for="item in scope.row.genotype.split(';')" :key="item">{{ item }}</div>
            </template>
          </template>
        </el-table-column>
        <el-table-column
          label="临床相关基因"
          prop="gene_list"
        >
          <template slot-scope="scope">
            <template v-if="scope.row.gene_list && scope.row.gene_list !== ''">
              <div v-for="item in scope.row.gene_list.split(';')" :key="item">{{ item }}</div>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="go-back-view">
      <el-button
        type="primary"
        plain
        @click="goBack"
      >返回</el-button>
      <el-button
        v-if="!detailDisable"
        type="primary"
        @click="submitForm"
      >保存</el-button>
    </div>
  </div>
</template>

<script>
import { updateClinicalinformation, getServiceCSData } from "@/api/caseManagement";

import FileUpload from "@/components/FileUpload";

export default {
  props: {
    patientData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    patientTypeMap: {
      type: Object,
      default: () => {
        return {}
      }
    },
    uploadType: {
      type: Array,
      default: () => {
        return []
      }
    },
    routerType: {
      type: String,
      default: ''
    }
  },
  components: {
    FileUpload
  },
  data() {
    const cellTherapyTextVal = (rule, value, callback) => {
      if (this.form.cellTherapy && value === '') {
        callback(new Error("请填写细胞治疗说明"));
      } else {
        callback();
      }
    };
    const rhealthTextVal = (rule, value, callback) => {
      if (this.form.rHealthType && value === '') {
        callback(new Error("请填写夫妻亲缘关系"));
      } else {
        callback();
      }
    };
    // const rjzqzVal = (rule, value, callback) => {
    //   if (this.form.asrjzqz && value === '') {
    //     callback(new Error("请选择是否诊断家族遗传病史"));
    //   } else {
    //     callback();
    //   }
    // };
    const rjzqzTextVal = (rule, value, callback) => {
      if (this.form.rjzqz && value === '') {
        callback(new Error("请填写家族遗传病史诊断描述"));
      } else {
        callback();
      }
    };
    const rfqTextVal = (rule, value, callback) => {
      if (this.form.rfqHealth && value === '') {
        callback(new Error("请填写姓名及检测结果"));
      } else {
        callback();
      }
    };
    const selectrPoorPregnancyVal = (rule, value, callback) => {
      if (this.form.asrPoorPregnancy && value.length === 0) {
        callback(new Error("请选择"));
      } else {
        callback();
      }
    };
    const rpoorTextVal = (rule, value, callback) => {
      if (this.form.asrPoorPregnancy && value === '') {
        callback(new Error("请填写不良孕产史描述"));
      } else {
        callback();
      }
    };
    const asGravidaWeekVal = (rule, value, callback) => {
      if (this.form.asGravida && value === '') {
        callback(new Error("请填写孕周"));
      } else {
        callback();
      }
    };
    const asGravidaDayVal = (rule, value, callback) => {
      if (this.form.asGravida && value === '') {
        callback(new Error("请填写孕天"));
      } else {
        callback();
      }
    };
    const asBabyTextVal = (rule, value, callback) => {
      if (this.form.asBaby && value === '') {
        callback(new Error("请填写胎儿异常描述"));
      } else {
        callback();
      }
    };
    const transplantTextVal = (rule, value, callback) => {
      if (this.form.transplant && value === '') {
        callback(new Error("请填写移植手术说明"));
      } else {
        callback();
      }
    };
    const bloodTextVal = (rule, value, callback) => {
      if (this.form.bloodOk && value === '') {
        callback(new Error("请填写异常输血说明"));
      } else {
        callback();
      }
    };
    const spouseCodeVal = (rule, value, callback) => {
      if (this.form.spouseHave && value === '') {
        callback(new Error("请填写检测编号"));
      } else {
        callback();
      }
    };
    const spouseResultsVal = (rule, value, callback) => {
      if (this.form.spouseHave && value === '') {
        callback(new Error("请填写检测结果"));
      } else {
        callback();
      }
    };
    return {
      info: {},
      isPregnantOption: [
        { value: true, label: '是' },
        { value: false, label: '否' },
      ],
      form: {
        id: '',
        userId: '',
        patientId: '',
        sampleStart: '',
        sampleEnd: '',
        inspectionMaterials: '',
        inspectionDepartment: '',
        inspectionDoctor:'',
        rHealthType: null,
        rhealthText: '',
        // asrjzqz: false,
        rjzqz: '',
        rjzqzText: '',
        rfqHealth: false,
        rfqText: '',
        asrPoorPregnancy: false, // 不良孕产史 是否有数据勾选，有为 true； 无为 false
        selectrPoorPregnancy: [],
        rPoorPregnancy: '', // 最后要将 selectrPoorPregnancy.join()
        rpoorText: '',
        rAuxiliary: false,
        rDonor: false,
        asGravida: '',
        asGravidaWeek: '',
        asGravidaDay: '',
        asBaby: '',
        asBabyText: '',
        cellTherapy: false,
        cellTherapyText: '',
        transplant: false,
        transplantText: '',
        bloodOk: false,
        bloodText: '',
        specialRemarks: '',
        spouseHave: '',
        spouseCode: '',
        spouseResults: '',
        proveUrl: '', // 知情同意书，统一修改
        asSync: '',
      },
      formRules: {
        cellTherapyText: [
          { required: true, validator: cellTherapyTextVal, trigger: "change" }
        ],
        rhealthText: [
          { required: true, validator: rhealthTextVal, trigger: "change" }
        ],
        // rjzqz: [
        //   { required: true, validator: rjzqzVal, trigger: "change" }
        // ],
        rjzqzText: [
          { required: true, validator: rjzqzTextVal, trigger: "change" }
        ],
        rfqText: [
          { required: true, validator: rfqTextVal, trigger: "change" }
        ],
        selectrPoorPregnancy: [
          { required: true, validator: selectrPoorPregnancyVal, trigger: "change" }
        ],
        rpoorText: [
          { required: true, validator: rpoorTextVal, trigger: "change" }
        ],
        asGravidaWeek: [
          { required: true, validator: asGravidaWeekVal, trigger: "change" }
        ],
        asGravidaDay: [
          { required: true, validator: asGravidaDayVal, trigger: "change" }
        ],
        asBabyText: [
          { required: true, validator: asBabyTextVal, trigger: "change" }
        ],
        transplantText: [
          { required: true, validator: transplantTextVal, trigger: "change" }
        ],
        bloodText: [
          { required: true, validator: bloodTextVal, trigger: "change" }
        ],
        spouseCode: [
          { required: true, validator: spouseCodeVal, trigger: "change" }
        ],
        spouseResults: [
          { required: true, validator: spouseResultsVal, trigger: "change" }
        ],
      },
      variationResult: [],
      detectionResult: [],
      testingReportSample: {},
      testingReportResult: {}
    }
  },
  computed: {
    detailDisable() {
      return this.routerType !== 'supplement'
    }
  },
  watch: {
    patientData: {
      handler(newValue) {
        if (Object.keys(newValue).length > 0) {
          this.getServiceCSDataRequest(newValue.pCode)
          this.info = JSON.parse(JSON.stringify(newValue))
          const formInfo = newValue.clinicalinformationVOList ? newValue.clinicalinformationVOList[0] : {}
          let data = {
            patientId: newValue.id,
            userId: newValue.userId,
          }
          if (Object.keys(formInfo).length > 0 && formInfo.rPoorPregnancy) {
            data = { ...data, ...{
              asrPoorPregnancy: formInfo.rPoorPregnancy.length > 0,
              selectrPoorPregnancy: formInfo.rPoorPregnancy.split(',')
            }}
          }
          this.form = { ...this.form, ...formInfo, ...data }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getServiceCSDataRequest(id) {
      getServiceCSData(id).then((response) => {
        this.testingReportSample = response.data.sample;
        this.testingReportResult = response.data.testResult;
      });
    },
    handleDownload() {

    },
    handlePreview() {

    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const params = JSON.parse(JSON.stringify(this.form))
          params.rPoorPregnancy = this.form.selectrPoorPregnancy.join()
          delete params['asrPoorPregnancy']
          delete params['selectrPoorPregnancy']
          updateClinicalinformation(params).then(res => {
            this.$modal.msgSuccess("修改成功");
            setTimeout(() => {
              this.goBack()
            }, 1000);
          })
        }
      });
    },
    goBack() {
      const obj = { path: "/caseManagement/case_list" };
      this.$tab.closeOpenPage(obj);
    }
  }
}
</script>
