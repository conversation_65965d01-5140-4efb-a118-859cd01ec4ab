<template>
  <div class="case-supplement">
    <!-- <div class="case-list-title">
      <div class="title-text">患者管理/患者病历信息</div>
    </div> -->
    <!-- 携带者 详情 -->
    <screening
      v-if="patientType === '1'"
      :patientData="patientData"
      :uploadType="uploadType"
      :patientTypeMap="patientTypeMap"
      :routerType="routerType"
    />
    <!-- 孕妇 详情 -->
    <gravidainfo
      v-else-if="patientType === '2'"
      :patientData="patientData"
      :uploadType="uploadType"
      :patientTypeMap="patientTypeMap"
      :routerType="routerType"
    />
    <!-- 新生儿 详情 -->
    <newbornbaby
      v-else-if="patientType === '3'"
      :patientData="patientData"
      :uploadType="uploadType"
      :patientTypeMap="patientTypeMap"
      :routerType="routerType"
    />
    <!-- 无创单病 详情 -->
    <NITP
      v-else-if="patientType === '4'"
      :patientData="patientData"
      :uploadType="uploadType"
      :patientTypeMap="patientTypeMap"
      :routerType="routerType"
    />
  </div>
</template>

<script>
import { getDoctorClientPatient } from "@/api/caseManagement";

import screening from "../case_supplement/screening.vue";
import gravidainfo from "../case_supplement/gravidainfo.vue";
import NITP from "../case_supplement/NITP.vue";
import newbornbaby from "../case_supplement/newbornbaby.vue";

import GlobalConstants from "@/constants.js";
export default {
  components: {
    screening,
    gravidainfo,
    newbornbaby,
    NITP,
  },
  data() {
    return {
      patientId: "",
      patientType: "", // 患者类型
      routerType: "", // 路由类型，是编辑 还是 详情
      uploadType: ["doc", "xls", "ppt", "pdf", "jpg", "jpeg", "png"],
      patientData: {},
    };
  },
  computed: {
    patientTypeMap() {
      return Object.fromEntries(GlobalConstants.PATIENT_TYPE_OPTION.map(Object.values));
    },
  },
  watch: {
    $route: {
      handler(newValue) {
        this.patientId = newValue.query.id;
        this.getDoctorClientPatientData();
        this.patientType = newValue.query.patientType;
        this.routerType = newValue.query.routerType;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getDoctorClientPatientData() {
      getDoctorClientPatient(this.patientId).then((response) => {
        this.patientData = response.data;
      });
    },
  },
};
</script>

<style lang="scss">
.case-supplement {
  padding: 30px;

  .case-list-title {
    margin: 10px 0 20px 0;
  }

  .mt30 {
    margin-top: 30px;
  }
  .ml20 {
    margin-left: 20px;
  }
  .mr20 {
    margin-right: 20px;
  }
  .mb20 {
    margin-bottom: 20px;
  }
  .weight500 {
    font-weight: 500;
  }
  .lh36 {
    line-height: 36px;
  }

  .el-date-editor {
    width: 100%;
  }

  .custom-form-view {
    display: flex;
  }
  .custom-form-title {
    width: 120px;
    flex-shrink: 0;
    font-weight: bold;
    line-height: 36px;
    text-align: right;
    font-size: 14px;
    color: #606266;
    padding-right: 12px;
  }

  .form-content-notitle .el-form-item__content {
    margin-left: 0 !important;
  }

  .display-flex {
    display: flex;
    .shrink {
      flex-shrink: 0;
    }
    .flex {
      flex: 1;
    }
  }

  .custom-form-item {
    line-height: 36px;
    margin-bottom: 22px;
  }

  .case-list-info {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .Patient-info {
      width: 100%;
      border: 1px solid #bfbfbf;
      padding: 30px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      ::v-deep .el-descriptions__header {
        width: 100%;
        justify-content: center;
      }
      ::v-deep .el-descriptions__title {
        font-size: 20px;
      }
    }
    .go-back-view {
      margin-top: 40px;
    }
  }

  .clinical-info {
    width: 100%;
    border: 1px solid #bfbfbf;
    margin-top: -1px;
    color: #606266;
    padding: 30px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    .clinical-info-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 10px;
    }
    ::v-deep .el-input__inner {
      width: 200px; //宽度
      height: 30px; //高度
      border-radius: 0; // 去除圆角
      border-top-width: 0px;
      border-left-width: 0px;
      border-right-width: 0px;
      border-bottom-width: 0px;
      border-color: #dcdfe6; //边框的颜色
    }
    .info-content {
      .info-item-view {
        line-height: 36px;
        font-size: 14px;
        color: #606266;
        .item-title {
          font-weight: bold;
          margin-right: 12px;
        }
      }
    }
    .clinical-info-form-blod {
      font-weight: bold;
      font-size: 16px;
      color: #000;
      margin: 15px;
    }
  }
}
</style>
