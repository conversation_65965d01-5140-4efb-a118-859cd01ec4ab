<template>
  <div class="case-supplement">
    <!-- <div class="case-list-title">
      <div class="title-text">患者管理/患者病历信息</div>
    </div> -->
    <div class="case-list-info">
      <div class="Patient-info">
        <el-descriptions title="患者信息">
          <el-descriptions-item label="姓名">{{ info.name }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ info.sex }}</el-descriptions-item>
          <el-descriptions-item label="年龄">{{ info.age }}</el-descriptions-item>
          <el-descriptions-item label="患者类型">{{ info.patientType }}</el-descriptions-item>
          <el-descriptions-item label="出生日期">{{ info.birthTime }}</el-descriptions-item>
          <el-descriptions-item label="民族">{{ info.nation }}</el-descriptions-item>
          <el-descriptions-item label="籍贯">{{ info.nativePlace }}</el-descriptions-item>
          <el-descriptions-item label="证件类型">{{ info.documentType }}</el-descriptions-item>
          <el-descriptions-item label="证件号码">{{ info.documentNum }}</el-descriptions-item>
          <el-descriptions-item label="联系方式">{{ info.contactInformation }}</el-descriptions-item>
          <el-descriptions-item label="是否上保险">{{ info.insurance }}</el-descriptions-item>
          <el-descriptions-item label="医院名称">{{ info.hospitalName }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="clinical-info">
        <div class="clinical-info-title">临床信息</div>
        <el-form
          ref="form"
          :model="form"
          label-width="120px"
        >
          <el-row
            align="middle"
            :gutter="10"
          >
            <el-col :span="8">
              <el-form-item label="采样时间：">
                <el-input v-model="form.samplingTime"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="收样时间：">
                <el-input v-model="form.closedTime"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="送检材料：">
                <el-input v-model="form.materials"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="送检科室：">
                <el-input v-model="form.administrativeOffice"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="送检医师：">
                <el-input v-model="form.medic"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="送检原因：">
                <el-input v-model="form.reason"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="目前是否怀孕：">
                <el-input v-model="form.pregnancy"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="孕周：">
                <el-input v-model="form.gestationalWeeks"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="孕天：">
                <el-input v-model="form.abnormal"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="胎儿有无异常：">
                <el-input v-model="form.description"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="胎儿异常描述：">
                <el-input v-model="form.heal"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="细胞治疗：">
                <el-input v-model="form.cure"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="细胞治疗说明：">
                <el-input v-model="form.instructions"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="配偶是否做过：">
                <el-input v-model="form.mate"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="检测编号：">
                <el-input v-model="form.ID"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="检测结果：">
                <el-input v-model="form.result"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="go-back-view">
        <el-button
          type="primary"
          plain
          @click="goBack"
        >返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      info: {
        name: '小王',
        sex: '女',
        age: 27,
        documentType: '居民身份证',
        documentNum: '787173838949487487365765347',
        patientType: '携带者筛查',
        contactInformation: '***********',
        birthTime: '1992-03-04',
        nation: '汉',
        nativePlace: '湖北武汉',
        insurance: '是',
        hospitalName: '武汉中心医院'
      },
      form: {
        samplingTime: '2023-08-28',
        closedTime: '2023-08-28',
        materials: '',
        administrativeOffice: '妇产科',
        medic:'王五',
        reason: '辅助生殖需要',
        pregnancy: '是',
        gestationalWeeks: '12+2',
        abnormal: '92',
        description: '有',
        heal: '心率不齐',
        cure: '心率不齐',
        instructions: '**********',
        mate: '是',
        ID: '2876543',
        result: '心率不齐'
      },
    }
  },
  methods: {
    goBack() {
      const obj = { path: "/caseManagement/case_list" };
      this.$tab.closeOpenPage(obj);
    }
  }
}
</script>

<style lang="scss" scoped>
.case-supplement {
  padding: 30px;

  .case-list-title {
    margin: 10px 0 20px 0;
  }

  .case-list-info {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .Patient-info {
      width: 100%;
      border: 1px solid #bfbfbf;
      padding: 30px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      ::v-deep .el-descriptions__header {
        width: 100%;
        justify-content: center;
      }
      ::v-deep .el-descriptions__title {
        font-size: 20px;
      }
    }
    .go-back-view {
      margin-top: 20px;
    }
  }

  .clinical-info {
    width: 100%;
    border: 1px solid #bfbfbf;
    border-top: 0;
    padding: 30px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    .clinical-info-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 10px;
    }
    ::v-deep .el-input__inner {
      width: 200px; //宽度
      height: 30px; //高度
      border-radius: 0; // 去除圆角
      border-top-width: 0px;
      border-left-width: 0px;
      border-right-width: 0px;
      border-bottom-width: 0px;
      border-color: #dcdfe6; //边框的颜色
    }
  }
}
</style>