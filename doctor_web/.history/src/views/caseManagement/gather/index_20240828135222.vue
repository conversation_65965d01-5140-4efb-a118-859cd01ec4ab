<template>
    <div class="referral-details">
      <!-- <div class="referral-details-title">转诊/发转诊 </div> -->
      <div class="referral-details-conter">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-position="top"
          label-width="120px"
          class="referral-details-input"
        >
          <el-form-item
            label="电子病历"
          >
          <file-upload v-model="form.proveUrl"  />
          </el-form-item>
          <el-form-item
            label="检验信息"
          >
          <file-upload v-model="form.proveUrl"  />
          </el-form-item>
          <el-form-item
            label="临床信息采集"
          >
          <file-upload v-model="form.proveUrl" />
          </el-form-item>
          <el-form-item
            label="影像诊断"
          >
          <file-upload v-model="form.proveUrl"  />
          </el-form-item>
          <el-form-item
            label="病理诊断"
          >
          <file-upload v-model="form.proveUrl"  />
          </el-form-item>
          <el-form-item
            label="基因检测实验室"
          >
          <file-upload v-model="form.proveUrl"  />
          </el-form-item>
       
        </el-form>
      </div>
    </div>
  </template>
  
  <script>
import FileUpload from "@/components/uploadFile";
  export default {
    components: {
        FileUpload
    },
    data() {
      return {
        hospitalOption: [],
        form: {
            proveUrl: '',
          reason: ''
        },
        rules: {
        //   hospital: [
        //     { required: true, message: "请选择医院名称", trigger: "blur" }
        //   ],
        //   reason: [
        //     { required: true, message: "请输入转诊原因", trigger: "blur" }
        //   ],
        }
      }
    },
    methods: {
      getHospital() {
        // listTable(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        //     this.hospitalOption = response.rows;
        //   }
        // );
      },
      submitForm() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            // updateHospital(this.form).then(res => {
            //   this.$modal.msgSuccess("修改成功");
            // });
          }
        });
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .referral-details {
    width: 100%;
    padding: 30px;
    .referral-details-title {
      margin: 20px 0 50px 0;
    }
    .referral-details-conter {
      padding-left: 30px;
      .referral-details-input {
        .el-select {
          width: 90%;
        }
        .el-textarea {
          width: 90%;
        }
      }
    }
  }
  </style>