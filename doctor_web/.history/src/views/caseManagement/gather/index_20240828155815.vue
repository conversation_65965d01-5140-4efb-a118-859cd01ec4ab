<template>
    <div class="referral-details">
      <!-- <div class="referral-details-title">转诊/发转诊 </div> -->
      <div class="referral-details-conter">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-position="top"
          label-width="120px"
          class="referral-details-input"
        >
          <el-form-item
            label="电子病历:"
          >
          <file-upload v-model="form.case" :pCode="pCode" fileType="1" @onSuccess="handlelist" />
          </el-form-item>
          <el-form-item
            label="检验信息:"
          >
          <file-upload  v-model="form.Inspection" :pCode="pCode" fileType="2" @onSuccess="handlelist/>
          </el-form-item>
          <el-form-item
            label="临床信息采集:"
          >
          <file-upload v-model="form.clinical" :pCode="pCode" fileType="3" @onSuccess="handlelist/>
          </el-form-item>
          <el-form-item
            label="影像诊断:"
          >
          <file-upload v-model="form.image" :pCode="pCode"  fileType="4" @onSuccess="handlelist/>
          </el-form-item>
          <el-form-item
            label="病理诊断:"
          >
          <file-upload v-model="form.pathology" :pCode="pCode" fileType="5" @onSuccess="handlelist/>
          </el-form-item>
          <el-form-item
            label="基因检测实验室:"
          >
          <file-upload   v-model="form.gene" :pCode="pCode" fileType="6" @onSuccess="handlelist/>
          </el-form-item>
       
        </el-form>
      </div>
        <div class="go-back-view">
      <el-button
        type="primary"
        plain
        @click="goBack"
      >返回</el-button>
    </div>
    </div>
  </template>
  
  <script>
import FileUpload from "@/components/uploadFile";
import { handlelist } from '@/api/caseManagement';
  export default {
    components: {
        FileUpload
    },
    watch: {
    $route: {
      handler(newValue) {
        this.pCode = newValue.query.pCode
      },
      deep: true,
      immediate: true
    }
  },
    data() {
      return {
        hospitalOption: [],
        form: {
            case:[],
            Inspection:[],
            clinical:[],
            image:[],
            pathology:[],
            gene:[]

        },
        pCode:'',
        rules: {
        //   hospital: [
        //     { required: true, message: "请选择医院名称", trigger: "blur" }
        //   ],
        //   reason: [
        //     { required: true, message: "请输入转诊原因", trigger: "blur" }
        //   ],
        }
      }
    },
    created() {
        this.handlelist()
    },
    methods: {
    
        goBack() {
      const obj = { path: "/caseManagement/case_list" };
      this.$tab.closeOpenPage(obj);
    },
    handlelist(){
        handlelist({
            pCode: this.pCode
      }).then(response => {
      console.log(response,'ooo');
      
      });
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .referral-details {
    width: 100%;
    padding: 30px;
    .referral-details-title {
      margin: 20px 0 50px 0;
    }
    .referral-details-conter {
      padding-left: 30px;
      .referral-details-input {
        .el-select {
          width: 90%;
        }
        .el-textarea {
          width: 90%;
        }
      }
    }
  }
  .go-back-view{
    text-align: center;
  }
  </style>