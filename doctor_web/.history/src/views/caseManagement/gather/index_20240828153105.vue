<template>
    <div class="referral-details">
      <!-- <div class="referral-details-title">转诊/发转诊 </div> -->
      <div class="referral-details-conter">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-position="top"
          label-width="120px"
          class="referral-details-input"
        >
          <el-form-item
            label="电子病历:"
          >
          <file-upload v-model="form.fileType1" :pCode="pCode" fileType="1" />
          </el-form-item>
          <el-form-item
            label="检验信息:"
          >
          <file-upload  v-model="form.fileType2" :pCode="pCode" fileType="2"/>
          </el-form-item>
          <el-form-item
            label="临床信息采集:"
          >
          <file-upload v-model="form.fileType3" :pCode="pCode" fileType="3"/>
          </el-form-item>
          <el-form-item
            label="影像诊断:"
          >
          <file-upload v-model="form.fileType4" :pCode="pCode"  fileType="4"/>
          </el-form-item>
          <el-form-item
            label="病理诊断:"
          >
          <file-upload v-model="form.fileType5" :pCode="pCode" fileType="5"/>
          </el-form-item>
          <el-form-item
            label="基因检测实验室:"
          >
          <file-upload   v-model="form.fileType6" :pCode="pCode" fileType="6"/>
          </el-form-item>
       
        </el-form>
      </div>
        <div class="go-back-view">
      <el-button
        type="primary"
        plain
        @click="goBack"
      >返回</el-button>
    </div>
    </div>
  </template>
  
  <script>
import FileUpload from "@/components/uploadFile";
  export default {
    components: {
        FileUpload
    },
    watch: {
    $route: {
      handler(newValue) {
        this.pCode = newValue.query.pCode
      },
      deep: true,
      immediate: true
    }
  },
    data() {
      return {
        hospitalOption: [],
        form: {
            
        },
        pCode:'',
        rules: {
        //   hospital: [
        //     { required: true, message: "请选择医院名称", trigger: "blur" }
        //   ],
        //   reason: [
        //     { required: true, message: "请输入转诊原因", trigger: "blur" }
        //   ],
        }
      }
    },
    methods: {
    
        goBack() {
      const obj = { path: "/caseManagement/case_list" };
      this.$tab.closeOpenPage(obj);
    },
    handlelist(){
        handlelist({
            pCode: this.pCode
      }).then(response => {
      console.log(response,'ooo');
      
      });
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .referral-details {
    width: 100%;
    padding: 30px;
    .referral-details-title {
      margin: 20px 0 50px 0;
    }
    .referral-details-conter {
      padding-left: 30px;
      .referral-details-input {
        .el-select {
          width: 90%;
        }
        .el-textarea {
          width: 90%;
        }
      }
    }
  }
  .go-back-view{
    text-align: center;
  }
  </style>