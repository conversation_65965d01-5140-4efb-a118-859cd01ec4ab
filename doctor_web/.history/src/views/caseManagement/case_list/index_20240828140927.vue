<template>
  <div class="case-list">
    <!-- <div class="case-list-title">患者管理/患者列表</div> -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="85px">
      <el-form-item label="关键词" prop="condition">
        <el-input v-model="queryParams.condition" placeholder="请输入患者名称/手机号搜索" width="120px" clearable
          suffix-icon="el-icon-search" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="证件号码" prop="idNum" wit>
        <el-input v-model="queryParams.idNum" placeholder="证件号码" clearable @keyup.enter.native="handleQuery"
          suffix-icon="el-icon-search" />
      </el-form-item>
      <el-form-item label="患者类型" prop="patientType" wit>
        <el-select v-model="queryParams.patientType" placeholder="请选择类型">
          <el-option v-for="dict in patientTypeOption" :key="dict.value" :label="dict.label"
            :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain size="mini" @click="handleImport">导入</el-button>
        <!-- v-hasPermi="['caseManagement:case_list:import']" -->
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain size="mini" @click="handleDownloadTemplate">下载模版</el-button>
        <!-- v-hasPermi="['caseManagement:case_list:downloadTemplate']" -->
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData" border style="width:100%">
      <el-table-column label="病历号/就诊卡号" prop="pCode" width="150" align="center">
      </el-table-column>
      <el-table-column label="父亲码" prop="fCode" width="150" align="center">
      </el-table-column>
      <el-table-column label="患者姓名" prop="name" width="150" align="center">
      </el-table-column>
      <el-table-column label="证件号码" prop="idNum" width="210" align="center">
      </el-table-column>
      <el-table-column label="年龄" prop="age" width="100" align="center">
      </el-table-column>
      <el-table-column label="性别" prop="gender" width="100" align="center">
        <template slot-scope="scope">{{ scope.row.gender === '0' ? '男' : scope.row.gender === '1' ? '女' :
          scope.row.gender }}</template>
      </el-table-column>
      <el-table-column label="手机号" prop="mobile" width="160" align="center">
      </el-table-column>
      <el-table-column label="患者类型" prop="patientType" width="140" align="center">
        <template slot-scope="scope">{{ patientTypeMap[scope.row.patientType] }}</template>
      </el-table-column>
      <el-table-column label="是否同步" prop="asSync" width="90" align="center">
        <template slot-scope="scope">{{ scope.row.asSync || scope.row.asSync === '1' || scope.row.asSync === 1 ? '是' :
          '否' }}</template>
      </el-table-column>
      <el-table-column label="第三方采样编号" prop="collectNum" width="160" align="center">
      </el-table-column>
      <el-table-column label="样本状态" prop="sampleStatus" width="90" align="center">
        <!-- <template slot-scope="scope">{{ scope.row.sampleStatus || scope.row.sampleStatus === '1' || scope.row.sampleStatus === 2 ? '已完成' : '检测中' }}</template> -->
        <template slot-scope="scope">{{ scope.row.reportId ? '已完成' : '检测中' }}</template>
      </el-table-column>
      <el-table-column label="检测结果" prop="reportResult" width="90" align="center">
        <template slot-scope="scope">{{ scope.row.reportId ? scope.row.reportResult : '/' }}</template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" min-width="200" align="center">
        <template slot-scope="scope">
          <el-button
            v-if="doctorData.hospId === scope.row.refHospitalNewId && (doctorData.manageAs || doctorData.id === scope.row.refDoctorId)"
            size="mini" type="text" @click="handleReferral(scope.row)"
            v-hasPermi="['caseManagement:case_list:referral']">转诊</el-button>
          <el-button size="mini" type="text" @click="handleNewPage(scope.row, 'details')"
            v-hasPermi="['caseManagement:case_list:details']">查看</el-button>
          <el-button size="mini" type="text" @click="handleNewPage(scope.row, 'gather')"
            v-hasPermi="['caseManagement:case_list:gather']">电子化采集</el-button>
          <el-button size="mini" type="text" @click="handleNewPage(scope.row, 'follow_up')"
            v-hasPermi="['followManagement:case_list:follow_up']">随访计划</el-button>
          <el-button size="mini" type="text" @click="handleNewPage(scope.row, 'referral_record')"
            v-hasPermi="['referralRecord:case_list:referral_record']">转诊记录</el-button>
          <el-button size="mini" type="text" @click="handleNewPage(scope.row, 'supplement')"
            v-hasPermi="['caseManagement:case_list:supplement']">病历补充</el-button>
          <el-button v-if="!scope.row.asDoctor" size="mini" type="text" @click="handleAssignDoctor(scope.row)"
            v-hasPermi="['caseManagement:case_list:assignDoctor']">分配医生</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            @click="handleShowPreview(scope.row)"
          >预览</el-button> -->
          <el-button size="mini" type="text" v-if="scope.row.reportId"
            @click="handlePreviewPdf(scope.row.pdfFileUrl)">预览</el-button>
          <!-- <el-button
              size="mini"
              type="text"
              v-if="scope.row.reportId "
              @click="handleDownloadTestReport(scope.row)"
            >下载</el-button> -->
          <el-dropdown v-if="scope.row.reportId">
            <span class="el-dropdown-link" style="color: #1890ff;">
              下载<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown" class="medical-dropdown">
              <el-dropdown-item @click.native="handleDownloadTestReport(scope.row, 'pdf')">pdf下载</el-dropdown-item>
              <el-dropdown-item @click.native="handleDownloadTestReport(scope.row, 'excel')">excel下载</el-dropdown-item>
              <el-dropdown-item @click.native="handleDownloadTestReport(scope.row, 'vcf')">vcf下载</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button size="mini" type="text" v-if="scope.row.reportId"
            @click="handleReferral(scope.row)">推送到小程序</el-button>
          <!-- <el-dropdown>
            <span class="el-dropdown-link">
              预览<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown" class="medical-dropdown">
              <el-dropdown-item @click.native="handleDownloadTestReport('/profile/upload/2024/07/01/001_20240701182706A005.pdf')">病历1</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
          <!-- <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['caseManagement:case_list:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog title="发起转诊" :visible.sync="openReferral" width="500px" append-to-body>
      <el-form ref="referralForm" :model="referralForm" :rules="referralRules" label-width="80px">
        <el-form-item label="转诊医院" prop="hospitalNewId">
          <el-select v-model="referralForm.hospitalNewId" placeholder="请选择转诊医院" clearable filterable
            style="width: 85%;">
            <!-- @change="changeHospitalItem" -->
            <el-option v-for="item in hospitalOption" :key="item.id" :label="item.hospName"
              :disabled="item.id === selectRowItem.refHospitalNewId" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="转诊原因" prop="referralText">
          <el-input placeholder="请输入转诊原因" type="textarea" :rows="3" v-model="referralForm.referralText" clearable
            style="width: 85%;"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitReferralForm">确 定</el-button>
        <el-button @click="cancelReferral">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="分配医生" :visible.sync="openAssignDoctor" width="500px" append-to-body>
      <el-form ref="assignDoctorForm" :model="assignDoctorForm" :rules="assignDoctorRules" label-width="80px">
        <el-form-item label="医生" prop="doctorId">
          <el-select v-model="assignDoctorForm.doctorId" placeholder="请选择" clearable filterable style="width: 80%;">
            <el-option v-for="item in doctorOption" :disabled="item.id === selectRowItem.refDoctorId" :key="item.id"
              :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAssignDoctorForm">确 定</el-button>
        <el-button @click="cancelAssignDoctor">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 一个用户多条PDF文件的时候 -->
    <!-- <el-dialog
      title="预览"
      :visible.sync="openPreviewDialog"
      width="500px"
      append-to-body
    >
      <el-table
        :data="tablePreviewData"
        border
        style="width: 100%"
      >
        <el-table-column
          label="检测报告"
          prop="pCode"
          width="170"
        />
        <el-table-column
        label="操作"
        fixed="right"
        min-width="120"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handlePreviewPdf(scope.row)"
            >预览</el-button>
            <el-button
              size="mini"
              type="text"
              @click="handleDownloadTestReport(scope.row)"
            >下载</el-button>
            <el-button
              size="mini"
              type="text"
              @click="handleReferral(scope.row)"
            >推送到小程序</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog> -->

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
        :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="text-center el-upload__tip" slot="tip">
          <!-- <div
            class="el-upload__tip"
            slot="tip"
          >
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div> -->
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="handleDownloadTemplate" v-hasPermi="['caseManagement:case_list:downloadTemplate']">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button
          type="primary"
          @click="submitFileForm"
        >确 定</el-button> -->
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getInfoDoctorClient, listDoctorClient, addPatientreferral, updatePatientreferral, pushReportToWx } from "@/api/caseManagement";
import { listDoctorClientDoctor } from "@/api/system/doctor";
import { listDoctorClientHospital, getlistDoctorClientHospital } from "@/api/system/hospital";

import { getToken } from "@/utils/auth";
import GlobalConstants from '@/constants.js'
export default {
  data() {
    return {
      showSearch: true,
      // 遮罩层
      loading: false,
      patientTypeOption: GlobalConstants.PATIENT_TYPE_OPTION,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        condition: '',
        idNum: '',
        patientType: '',
      },
      // 总条数
      total: 0,
      tableData: [],
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "导入",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/doctorClient/importExcel"
      },
      selectRowItem: {},
      hospitalOption: [],
      openReferral: false,
      referralForm: {
        patientId: '',
        hospitalNewId: '',
        referralText: ''
      },
      referralRules: {
        hospitalNewId: [
          { required: true, message: "请选择医院名称", trigger: "blur" }
        ],
        referralText: [
          { required: true, message: "请输入转诊原因", trigger: "blur" }
        ],
      },
      doctorData: {},

      doctorOption: [],
      openPreviewDialog: false,
      tablePreviewData: [],
      openAssignDoctor: false,
      assignDoctorForm: {
        id: null,
        doctorId: '',
      },
      assignDoctorRules: {
        doctorId: [
          { required: true, message: "请选择医生", trigger: "blur" }
        ],
      }
    }
  },
  computed: {
    patientTypeMap() {
      const data = {}
      this.patientTypeOption.forEach(ele => {
        data[ele.value] = ele.label
      })
      return data
    }
  },
  created() {
    this.getInfoDoctor();
    this.getList();
    this.getHospital();
  },
  methods: {
    getList() {
      this.loading = true;
      listDoctorClient(this.queryParams).then(response => {
        this.tableData = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getInfoDoctor() {
      getInfoDoctorClient().then(response => {
        this.doctorData = response.data;
      });
    },
    getHospital() {
      getlistDoctorClientHospital().then(response => {
        this.hospitalOption = response.data;
      });
    },
    getDoctor() {
      listDoctorClientDoctor({
        hospId: this.selectRowItem.refHospitalNewId
      }).then(response => {
        this.doctorOption = response.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 转诊按钮操作 */
    handleReferral(row) {
      this.$confirm('此操作将推送报告到小程序, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          pcode: row.pCode,
          reportId: row.reportId
        }
        pushReportToWx(params).then(response => {
          if (response.code !== 200) {
            this.$modal.msgError(response.msg || '推送失败');
          }
          this.$modal.msgSuccess("推送成功");
        }).catch(err => {
          console.log(err)
        })
      }).catch(() => {
      });

      //   this.resetForm("referralForm");
      //   this.selectRowItem = row
      //   this.referralForm.patientId = row.id
      //   this.openReferral = true
    },
    // changeHospitalItem(val) {
    //   this.referralForm.hospitalNewName = this.hospitalOption.filter(ele => {
    //     return ele.id === val
    //   })[0].hospName
    // },
    submitReferralForm() {
      this.$refs["referralForm"].validate(valid => {
        if (valid) {
          const hospData = this.hospitalOption.filter(ele => {
            return ele.id === this.referralForm.hospitalNewId
          })[0]
          addPatientreferral({
            ...this.referralForm, ...{
              hospitalNewName: hospData.hospName,
              patientName: this.selectRowItem.name,
              userId: this.selectRowItem.userId
            }
          }).then(response => {
            this.$modal.msgSuccess("转诊成功");
            this.openReferral = false;
            this.getList();
          });
        }
      });
    },
    cancelReferral() {
      this.openReferral = false
    },

    /** 跳转页面： 查看、病历补充、转诊记录、随访计划 按钮操作 */
    handleNewPage(row, type) {
      let path = null
      if (type == 'follow_up') {
        path = 'followManagement'
      } else if (type == 'referral_record') {
        path = 'referralRecord'
      } else {
        path = 'caseManagement'
      }
      this.$router.push({
        path: `/${path}/${type}/index`,
        query: {
          id: row.id,
          hospId: row.hospId,
          patientType: row.patientType,
          routerType: type,
          pCode:row.pCode
        }
      });
    },
    /** 分配医生按钮操作 */
    handleAssignDoctor(row) {
      this.selectRowItem = row
      this.getDoctor()
      this.resetForm("assignDoctorForm");
      this.openAssignDoctor = true
    },
    submitAssignDoctorForm() {
      this.$refs["assignDoctorForm"].validate(valid => {
        if (valid) {
          const params = {
            doctorId: this.assignDoctorForm.doctorId,
            id: this.selectRowItem.referralId,
            // hospitalOldId: this.selectRowItem.refHospitalNewId,
            userId: this.selectRowItem.userId,
            // patientId: this.selectRowItem.id
          }
          updatePatientreferral(params).then(response => {
            this.$modal.msgSuccess("分配医生成功");
            this.openAssignDoctor = false;
            this.getList();
          });
        }
      });
    },
    cancelAssignDoctor() {
      this.openAssignDoctor = false
    },

    /** 删除按钮操作 */
    // handleDelete(row) {
    //   const ids = row.id || this.ids;
    //   this.$modal.confirm('是否确认删除病历编号为"' + ids + '"的数据项？').then(function() {
    //     // return delDoctor(ids);
    //   }).then(() => {
    //     this.getList();
    //     this.$modal.msgSuccess("删除成功");
    //   }).catch(() => {});
    // },
    handleShowPreview(row) {
      this.selectRowItem = row
      this.getTestReport()
      this.openPreviewDialog = true
    },
    getTestReport() {
      listDoctorClientDoctor({
        id: this.selectRowItem.id
      }).then(response => {
        this.tablePreviewData = response.data;
      });
    },
    handlePreviewPdf(pdf) {
      console.log('handlePreviewPdf', pdf);
      if (pdf) {
        window.open(process.env.VUE_APP_BASE_API + pdf, "_blank");
      } else {
        this.$modal.msgError('检测报告不存在');
      }
    },
    handleDownloadTestReport(row, type) {
      console.log('handleDownloadTestReport', type);
      let url
      if (type == 'pdf') {
        url = process.env.VUE_APP_BASE_API + row.pdfFileUrl;
      } else if (type == 'excel') {
        url = process.env.VUE_APP_BASE_API + row.excelFileUrl;
      } else if (type == 'vcf') {
        url = process.env.VUE_APP_BASE_API + row.vcfFileUrl;
      }


      // 创建一个临时的URL对象
      // const url = URL.createObjectURL(blob);
      // 创建一个隐藏的<a>标签，并设置其href属性为临时URL
      const a = document.createElement('a');
      a.href = url;
      a.download = row.name; // 设置下载的文件名
      a.style.display = 'none';
      // 将<a>标签添加到文档中，并模拟点击下载
      document.body.appendChild(a);
      a.click();
      // 下载完成后，移除<a>标签和临时URL对象
      document.body.removeChild(a);
      //   URL.revokeObjectURL(url);
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.open = true;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      const msg = response.code === 200 ? '导入成功' : response.msg
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 下载模版按钮操作 */
    handleDownloadTemplate() {
      this.download('/doctorClient/import', {}, `病历信息模版_${new Date().getTime()}.xlsx`)
    }
  },
}
</script>

<style lang="scss">
.medical-dropdown {
  min-width: 150px;
}
</style>

<style lang="scss" scoped>
.case-list {
  padding: 20px;

  .case-list-title {
    margin-bottom: 30px;
  }

  .case-list-btn {
    display: flex;
    margin-bottom: 20px;

    .btn {
      width: 100px;
      height: 35px;
      border: 1px solid #bfbfbf;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 5px;
      margin-right: 10px;
      font-size: 14px;
      cursor: pointer;
    }
  }

  .pagination_wrap {
    margin-top: 20px;
  }
}
</style>