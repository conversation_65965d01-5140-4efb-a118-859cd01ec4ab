<template>
  <div class="case-list-info">
    <!-- 无创单病 -->
    <div class="clinical-info">
      <div class="clinical-info-title">患者信息</div>
      <el-row style="width: 100%" :gutter="10" class="info-content">
        <el-col :span="6" v-for="item of baseInfos" :key="item.label">
          <div class="info-item-view"
            ><span class="item-title">{{ item.label }}: </span
            >{{ info[item.valueKey[0]] || (item.valueKey[1] ? info[item.valueKey[1]] : "-") || "-" }}</div
          >
        </el-col>
      </el-row>
    </div>

    <div class="clinical-info">
      <div class="clinical-info-title">基本信息</div>
      <el-form ref="baseForm" :model="nsdBaseInfo" :rules="formRules" label-width="120px" style="width: 100%">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="患者类型">无创单病</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="样本编号：">{{ info.pCode }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="第三方采样编号">
              <el-input v-model="info.collectNum" />
            </el-form-item>
          </el-col>
         
          <el-col :span="8">
            <el-form-item label="采血时间:">
              <el-date-picker
                clearable
                v-model="nsdBaseInfo.bloodCollectionTime"
                type="datetime"
                :disabled="detailDisable"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="门诊号：">
              <el-input v-model="nsdBaseInfo.nCode" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="民族：">
              <el-input v-model="nsdBaseInfo.gClan" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="配偶出生日期：" prop="spouseBirthDate" label-width="130px" class="flex">
              <el-date-picker
                clearable
                v-model="nsdBaseInfo.spouseBirthDate"
                type="date"
                :disabled="detailDisable"
                value-format="yyyy-MM-dd"
                placeholder="请选择时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="配偶民族：">
              <el-input v-model="nsdBaseInfo.spouseEthnicGroup" :disabled="detailDisable"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="籍贯：">
              <el-input
                v-model="nsdBaseInfo.nativePlace"
                :disabled="detailDisable"
                placeholder="x省x市x区/县"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="孕妇文化程度：">
              <el-radio-group v-model="nsdBaseInfo.maternalEducation" :disabled="detailDisable" class="mr20">
                <el-radio v-for="level of educationLevelOptions" :label="level.value" :key="level.value">{{
                  level.label
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="配偶文化程度：">
              <el-radio-group v-model="nsdBaseInfo.spouseEducation" :disabled="detailDisable" class="mr20">
                <el-radio v-for="level of educationLevelOptions" :label="level.value" :key="level.value">{{
                  level.label
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="display-flex">
            <el-form-item label="月经是否规律：">
              <el-radio-group v-model="nsdBaseInfo.menstrualRegular" :disabled="detailDisable" class="mr20">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="月经周期：" prop="menstrualCycle" label-width="170px" class="flex">
              <div class="display-flex">
                <el-input
                  style="width: 80px"
                  class="mr20"
                  v-model="nsdBaseInfo.menstrualStartDay"
                  :disabled="detailDisable"
                />
                <template v-if="!nsdBaseInfo.menstrualRegular">
                  <span class="mr20">~</span>
                  <el-input
                    style="width: 80px"
                    class="mr20"
                    v-model="nsdBaseInfo.menstrualEndDay"
                    :disabled="detailDisable" /></template
                >天
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="孕周(超声核实)：" class="flex">
              <div class="display-flex">
                <el-input style="width: 80px" class="mr20" v-model="nsdBaseInfo.gWeek" :disabled="detailDisable" />
                <span class="mr20">周</span>
                <el-input style="width: 80px" class="mr20" v-model="nsdBaseInfo.gDay" :disabled="detailDisable" />天
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身高：">
              <el-input v-model="nsdBaseInfo.gHeight" :disabled="detailDisable"
                ><template slot="append">cm</template></el-input
              >
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="采血时体重：">
              <el-input v-model="nsdBaseInfo.gWeight" :disabled="detailDisable"
                ><template slot="append">Kg</template></el-input
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="clinical-info">
      <div class="clinical-info-title">简要病史</div>
      <el-form
        ref="medicalHistoryForm"
        :model="nsdMedicalHistory"
        :rules="formRules"
        label-width="120px"
        style="width: 100%"
      >
        <el-row :gutter="10">
          <el-col :span="24">
            <div class="clinical-info-form-blod">本次妊娠情况</div>
          </el-col>
          <el-col :span="8">
            <el-form-item label="自然受孕：">
              <el-radio-group v-model="nsdMedicalHistory.naturalConception" :disabled="detailDisable" class="mr20">
                <!-- (0表示否，1表示是) -->
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="促排卵：">
              <el-radio-group v-model="nsdMedicalHistory.ovulationInduction" :disabled="detailDisable" class="mr20">
                <!-- (0表示否，1表示是) -->
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="宫腔内人工授精(IUI)：" label-width="160px">
              <el-radio-group v-model="nsdMedicalHistory.iui" :disabled="detailDisable" class="mr20">
                <!-- (0表示否，1表示是) -->
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-col :span="8">
              <el-form-item label="体外受精-胚胎移植(IVF-ET)：" label-width="200px">
                <el-radio-group v-model="nsdMedicalHistory.ivfEt" :disabled="detailDisable" class="mr20">
                  <!-- (0表示否，1表示是) -->
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-col>
          <template v-if="nsdMedicalHistory.ivfEt">
            <el-col :span="8">
              <el-form-item label="胚胎移植时间：">
                <el-date-picker
                  clearable
                  v-model="nsdMedicalHistory.transplantationDate"
                  type="datetime"
                  :disabled="detailDisable"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="请选择时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="胚胎移植数：">
                <el-input v-model="nsdMedicalHistory.transplantationNumber" :disabled="detailDisable"
                  ><template slot="append">个</template></el-input
                >
              </el-form-item>
            </el-col>
            <el-col :span="8"
              ><el-form-item label="存活胚胎数量：">
                <el-input v-model="nsdMedicalHistory.survivingEmbryos" :disabled="detailDisable"
                  ><template slot="append">个</template></el-input
                >
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="24">
            <div class="clinical-info-form-blod">孕产史</div>
          </el-col>
          <el-col :span="24">
            <el-col :span="8">
              <el-form-item label="孕次：">
                <el-input v-model="nsdMedicalHistory.pregnancies" :disabled="detailDisable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="产次：">
                <el-input v-model="nsdMedicalHistory.deliveries" :disabled="detailDisable"></el-input>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24"
            ><el-col :span="8">
              <el-form-item label="不良孕产史：">
                <el-radio-group v-model="nsdMedicalHistory.adversePregnancy" :disabled="detailDisable" class="mr20">
                  <!-- 不良孕产史 (0表示否，1表示是) -->
                  <el-radio :label="0">无</el-radio>
                  <el-radio :label="1">有</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-col>
          <template v-if="nsdMedicalHistory.adversePregnancy">
            <el-col :span="8">
              <el-form-item label="自然流产次：">
                <el-input v-model="nsdMedicalHistory.spontaneousAbortions" :disabled="detailDisable"></el-input>
              </el-form-item>
            </el-col>
            <!-- FIXME 这里后端缺少字段 -->
            <el-col :span="8">
              <el-form-item label="死胎次：">
                <el-input v-model="nsdMedicalHistory.survivingEmbryos" :disabled="detailDisable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="新生儿死亡次：">
                <el-input v-model="nsdMedicalHistory.neonatalDeaths" :disabled="detailDisable"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="缺陷儿史次：">
                <div class="display-flex">
                  <el-input
                    style="width: 120px"
                    class="mr20"
                    v-model="nsdMedicalHistory.congenitalDefectHistory"
                    :disabled="detailDisable"
                  ></el-input>
                  <span class="mr20">（缺陷名：</span
                  ><el-input
                    style="width: 200px"
                    class="mr20"
                    v-model="nsdMedicalHistory.congenitalDefectName"
                    :disabled="detailDisable"
                  ></el-input
                  >）
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="其他：">
                <el-input v-model="nsdMedicalHistory.congenitalDefectOther" :disabled="detailDisable"></el-input>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="24">
            <el-form-item label="目前患何种疾病：" label-width="150px">
              <el-checkbox-group v-model="nsdMedicalHistory.currentDiseases">
                <el-checkbox v-for="disease in diseaseOptions" :label="disease.value" :key="disease.value">{{
                  disease.label
                }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div class="clinical-info-form-blod">既往史</div>
          </el-col>
          <el-col :span="24" class="display-flex">
            <el-col :span="4">
              <el-checkbox
                v-model="nsdMedicalHistory.bloodTransfusionHistory"
                :true-label="1"
                :false-label="0"
                :disabled="detailDisable"
                class="custom-form-item ml20 mr20 shrink"
                >异体输血</el-checkbox
              >
            </el-col>
            <template v-if="nsdMedicalHistory.bloodTransfusionHistory">
              <el-col :span="8">
                <el-form-item label="日期：">
                  <el-date-picker
                    clearable
                    v-model="nsdMedicalHistory.bloodTransfusionDate"
                    type="datetime"
                    :disabled="detailDisable"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="请选择时间"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </template>
          </el-col>
          <el-col :span="24" class="display-flex">
            <el-col :span="4">
              <el-checkbox
                v-model="nsdMedicalHistory.transplantationHistory"
                :true-label="1"
                :false-label="0"
                :disabled="detailDisable"
                class="custom-form-item ml20 mr20 shrink"
                >移植手术</el-checkbox
              >
            </el-col>
            <template v-if="nsdMedicalHistory.transplantationHistory">
              <el-col :span="8">
                <el-form-item label="日期：">
                  <el-date-picker
                    clearable
                    v-model="nsdMedicalHistory.transplantationDate2"
                    type="datetime"
                    :disabled="detailDisable"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="请选择时间"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </template>
          </el-col>
          <el-col :span="24" class="display-flex">
            <el-col :span="4">
              <el-checkbox
                v-model="nsdMedicalHistory.allogeneicCellTherapyHistory"
                :true-label="1"
                :false-label="0"
                :disabled="detailDisable"
                class="custom-form-item ml20 mr20 shrink"
                >异体细胞治疗</el-checkbox
              >
            </el-col>
            <template v-if="nsdMedicalHistory.allogeneicCellTherapyHistory">
              <el-col :span="8">
                <el-form-item label="日期：">
                  <el-date-picker
                    clearable
                    v-model="nsdMedicalHistory.allogeneicCellTherapyDate"
                    type="datetime"
                    :disabled="detailDisable"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="请选择时间"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </template>
          </el-col>
          <el-col :span="24" class="display-flex">
            <el-col :span="4">
              <el-checkbox
                v-model="nsdMedicalHistory.stemCellTherapyHistory"
                :true-label="1"
                :false-label="0"
                :disabled="detailDisable"
                class="custom-form-item ml20 mr20 shrink"
                >干细胞治疗</el-checkbox
              >
            </el-col>
            <template v-if="nsdMedicalHistory.stemCellTherapyHistory">
              <el-col :span="8">
                <el-form-item label="日期：">
                  <el-date-picker
                    clearable
                    v-model="nsdMedicalHistory.stemCellTherapyDate"
                    type="datetime"
                    :disabled="detailDisable"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="请选择时间"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </template>
          </el-col>
          <el-col :span="24" class="display-flex">
            <el-col :span="4">
              <el-checkbox
                v-model="nsdMedicalHistory.cancerHistoryHistory"
                :true-label="1"
                :false-label="0"
                :disabled="detailDisable"
                class="custom-form-item ml20 mr20 shrink"
                >肿瘤病史</el-checkbox
              >
            </el-col>
            <template v-if="nsdMedicalHistory.cancerHistoryHistory">
              <el-col :span="8">
                <el-form-item label="日期：">
                  <el-date-picker
                    clearable
                    v-model="nsdMedicalHistory.cancerHistoryDate"
                    type="date"
                    :disabled="detailDisable"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择时间"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="肿瘤疾病名称：">
                  <el-input v-model="nsdMedicalHistory.cancerDiseaseName" :disabled="detailDisable"></el-input>
                </el-form-item>
              </el-col>
            </template>
          </el-col>
          <el-col :span="24" class="display-flex">
            <el-col :span="4">
              <el-checkbox
                v-model="nsdMedicalHistory.personalGeneticHistory"
                :true-label="1"
                :false-label="0"
                :disabled="detailDisable"
                class="custom-form-item ml20 mr20 shrink"
                >本人遗传病史</el-checkbox
              >
            </el-col>
            <template v-if="nsdMedicalHistory.personalGeneticHistory">
              <el-col :span="8">
                <el-form-item label="描述病史信息：">
                  <el-input v-model="nsdMedicalHistory.personalGeneticDescription" :disabled="detailDisable"></el-input>
                </el-form-item>
              </el-col>
            </template>
          </el-col>
          <el-col :span="24" class="display-flex">
            <el-col :span="4">
              <el-checkbox
                v-model="nsdMedicalHistory.familyGeneticHistory"
                :true-label="1"
                :false-label="0"
                :disabled="detailDisable"
                class="custom-form-item ml20 mr20 shrink"
                >家族遗传病史</el-checkbox
              >
            </el-col>
            <template v-if="nsdMedicalHistory.familyGeneticHistory">
              <el-col :span="8">
                <el-form-item label="描述家系信息：">
                  <el-input v-model="nsdMedicalHistory.familyGeneticDescription" :disabled="detailDisable"></el-input>
                </el-form-item>
              </el-col>
            </template>
          </el-col>
          <el-col :span="24" class="display-flex">
            <el-col :span="4">
              <el-checkbox
                v-model="nsdMedicalHistory.singleGeneHistory"
                :true-label="1"
                :false-label="0"
                :disabled="detailDisable"
                class="custom-form-item ml20 mr20 shrink"
                >单基因遗传病家族史</el-checkbox
              >
            </el-col>
            <template v-if="nsdMedicalHistory.singleGeneHistory">
              <el-col :span="8">
                <el-form-item label="描述家系信息：">
                  <el-input v-model="nsdMedicalHistory.singleGeneDescription" :disabled="detailDisable"></el-input>
                </el-form-item>
              </el-col>
            </template>
          </el-col>
          <el-col :span="24" class="display-flex">
            <el-col :span="4">
              <el-checkbox
                v-model="nsdMedicalHistory.geneticTesting"
                :true-label="1"
                :false-label="0"
                :disabled="detailDisable"
                class="custom-form-item ml20 mr20 shrink"
                >夫妻双方是否做过基因检测</el-checkbox
              >
            </el-col>
            <template v-if="nsdMedicalHistory.geneticTesting">
              <el-col :span="8">
                <el-form-item label="描述检测信息：">
                  <el-input v-model="nsdMedicalHistory.geneticTestingDescription" :disabled="detailDisable"></el-input>
                </el-form-item>
              </el-col>
            </template>
          </el-col>
          <el-col :span="24" class="display-flex">
            <el-col :span="4">
              <el-checkbox
                v-model="nsdMedicalHistory.consanguinityHistory"
                :true-label="1"
                :false-label="0"
                :disabled="detailDisable"
                class="custom-form-item ml20 mr20 shrink"
                >近亲婚配史</el-checkbox
              >
            </el-col>
            <template v-if="nsdMedicalHistory.consanguinityHistory">
              <el-col :span="8">
                <el-form-item label="注明关系：">
                  <el-input v-model="nsdMedicalHistory.consanguinityRelation" :disabled="detailDisable"></el-input>
                </el-form-item>
              </el-col>
            </template>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="clinical-info">
      <div class="clinical-info-title">辅助检查</div>
      <el-form
        ref="nsdAuxiliaryExaminationsForm"
        :model="nsdAncillaryExamination"
        :rules="formRules"
        label-width="120px"
        style="width: 100%"
      >
        <el-row :gutter="10">
          <!-- <div class="clinical-info-form-blod">本次妊娠情况</div> -->
          <el-col :span="24">
            <el-col :span="24">
              <el-form-item label="超声检查：">
                <el-radio-group v-model="nsdAncillaryExamination.ultrasound" :disabled="detailDisable" class="mr20">
                  <!-- (1表示单胎，2表示双胎) -->
                  <el-radio :label="1">单胎</el-radio>
                  <el-radio :label="2">双胎</el-radio>
                </el-radio-group>
                <template v-if="nsdAncillaryExamination.ultrasound === 2">
                  <span class="mr20">（</span>
                  <el-radio-group
                    v-model="nsdAncillaryExamination.ultrasoundType"
                    :disabled="detailDisable"
                    class="mr20"
                  >
                    <!-- TODO 需要和后端确认，不用枚举？ 单绒单羊、单绒双羊、双绒双羊、未知 -->
                    <el-radio :label="1">单绒单羊</el-radio>
                    <el-radio :label="2">单绒双羊</el-radio>
                    <el-radio :label="3">双绒双羊</el-radio>
                    <el-radio :label="4">未知</el-radio> </el-radio-group
                  >）
                </template>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24"
            ><el-col :span="8">
              <el-form-item label="超声异常发现：">
                <el-radio-group
                  v-model="nsdAncillaryExamination.ultrasoundAbnormalities"
                  :disabled="detailDisable"
                  class="mr20"
                >
                  <!-- 不良孕产史 (0表示否，1表示是) -->
                  <el-radio :label="0">无</el-radio>
                  <el-radio :label="1">有</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <div class="clinical-info-form-blod">筛查模式</div>
          </el-col>
          <el-col :span="24"
            ><el-col :span="5">
              <el-form-item label="NT检查：">
                <el-radio-group v-model="nsdAncillaryExamination.ntCheck" :disabled="detailDisable" class="mr20">
                  <!-- 不良孕产史 (0表示否，1表示是) -->
                  <el-radio :label="0">未做</el-radio>
                  <el-radio :label="1">已做</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <template v-if="nsdAncillaryExamination.ntCheck">
              <el-col :span="8">
                <el-form-item label="检查日期：">
                  <el-date-picker
                    clearable
                    v-model="nsdAncillaryExamination.ntCheckDate"
                    type="date"
                    :disabled="detailDisable"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择时间"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="测定孕周：" class="flex">
                  <div class="display-flex">
                    <el-input
                      style="width: 80px"
                      class="mr20"
                      v-model="nsdAncillaryExamination.ntCheckGestationalWeeks"
                      :disabled="detailDisable"
                    />
                    <span class="mr20">周</span>
                    <!-- FIXME 这里后端缺少 天的 字段 -->
                    <el-input
                      style="width: 80px"
                      class="mr20"
                      v-model="nsdAncillaryExamination.ntCheckGestationalDay"
                      :disabled="detailDisable"
                    />天
                  </div>
                </el-form-item>
              </el-col>
            </template>
          </el-col>
          <el-col :span="24">
            <el-col :span="16">
              <el-form-item label="顶臀长(CRL)：">
                <div class="display-flex">
                  <el-input class="mr20" v-model="nsdAncillaryExamination.crlFetus1" :disabled="detailDisable"
                    ><template slot="prepend">胎儿1</template><template slot="append">mm</template></el-input
                  >
                  <el-input v-model="nsdAncillaryExamination.crlFetus2" :disabled="detailDisable"
                    ><template slot="prepend">胎儿2</template><template slot="append">mm</template></el-input
                  >
                </div>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="16">
              <el-form-item label="NT测定值胎：">
                <div class="display-flex">
                  <el-input class="mr20" v-model="nsdAncillaryExamination.ntMeasurementFetus1" :disabled="detailDisable"
                    ><template slot="prepend">胎儿1</template><template slot="append">mm</template></el-input
                  >
                  <el-input v-model="nsdAncillaryExamination.ntMeasurementFetus2" :disabled="detailDisable"
                    ><template slot="prepend">胎儿2</template><template slot="append">mm</template></el-input
                  >
                </div>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item label="母血清学筛查模式及风险率：" label-width="200px">
                <el-radio-group v-model="nsdAncillaryExamination.serumScreening" :disabled="detailDisable" class="mr20">
                  <el-radio :label="0">未做</el-radio>
                  <el-radio :label="1">已做</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-col :span="8">
              <el-form-item label="孕期21-三体：">
                <el-radio-group v-model="nsdAncillaryExamination.trisomy21" :disabled="detailDisable" class="mr20">
                  <el-radio :label="1">高风险</el-radio>
                  <el-radio :label="0">低风险</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="孕期18-三体：">
                <el-radio-group v-model="nsdAncillaryExamination.trisomy18" :disabled="detailDisable" class="mr20">
                  <el-radio :label="1">高风险</el-radio>
                  <el-radio :label="0">低风险</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <div class="display-flex">
              <el-form-item label="开放性神经管缺陷(ONTD)：" label-width="200px">
                <el-radio-group v-model="nsdAncillaryExamination.ontd" :disabled="detailDisable" class="mr20">
                  <el-radio :label="1">高风险</el-radio>
                  <el-radio :label="0">低风险</el-radio>
                  <el-radio :label="2">其他</el-radio>
                </el-radio-group>
              </el-form-item>
              <!-- FIXME 这里后端缺少 其他手动输入 字段 -->
              <template v-if="nsdAncillaryExamination.ontd === 2">
                <el-input
                  style="width: 500px"
                  v-model="nsdAncillaryExamination.ontdOther"
                  :disabled="detailDisable"
                ></el-input>
              </template>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="display-flex">
              <el-form-item label="女方染色体：">
                <el-radio-group
                  v-model="nsdAncillaryExamination.femaleChromosomes"
                  :disabled="detailDisable"
                  class="mr20"
                >
                  <el-radio :label="0">未做</el-radio>
                  <el-radio :label="1">正常</el-radio>
                  <el-radio :label="2">其他</el-radio>
                </el-radio-group>
              </el-form-item>
              <!-- FIXME 这里后端缺少 其他手动输入 字段 -->
              <template v-if="nsdAncillaryExamination.femaleChromosomes === 2">
                <el-input
                  style="width: 500px"
                  v-model="nsdAncillaryExamination.femaleChromosomesOther"
                  :disabled="detailDisable"
                ></el-input>
              </template>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="display-flex">
              <el-form-item label="男方染色体：">
                <el-radio-group
                  v-model="nsdAncillaryExamination.maleChromosomes"
                  :disabled="detailDisable"
                  class="mr20"
                >
                  <el-radio :label="0">未做</el-radio>
                  <el-radio :label="1">正常</el-radio>
                  <el-radio :label="2">其他</el-radio>
                </el-radio-group>
              </el-form-item>
              <!-- FIXME 这里后端缺少 其他手动输入 字段 -->
              <template v-if="nsdAncillaryExamination.maleChromosomes === 2">
                <el-input
                  style="width: 500px"
                  v-model="nsdAncillaryExamination.maleChromosomesOther"
                  :disabled="detailDisable"
                ></el-input>
              </template>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="clinical-info">
      <div class="clinical-info-title">临床诊断</div>
      <el-form ref="nsdOtherInfoForm" :model="nsdOtherInfo" :rules="formRules" label-width="120px" style="width: 100%">
        <el-row :gutter="10">
          <!-- <div class="clinical-info-form-blod">本次妊娠情况</div> -->
          <el-col :span="24">
            <el-form-item label="妊娠诊断：">
              <el-input
                type="textarea"
                :rows="3"
                v-model="nsdOtherInfo.pregnancyDiagnosis"
                :disabled="detailDisable"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="超声结果：">
              <el-input
                type="textarea"
                :rows="3"
                v-model="nsdOtherInfo.ultrasoundResult"
                :disabled="detailDisable"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div class="clinical-info-form-blod">辅助检查报告</div>
          </el-col>
          <el-col :span="24">
            <div class="display-flex">
              <el-form-item label="胎儿影像学报告：" label-width="130px">
                <el-radio-group v-model="nsdOtherInfo.fetalImagingReport" :disabled="detailDisable" class="mr20">
                  <el-radio :label="0">无</el-radio>
                  <el-radio :label="1">有</el-radio>
                </el-radio-group>
              </el-form-item>
              <template v-if="nsdOtherInfo.fetalImagingReport === 1">
                <file-upload
                  v-model="nsdOtherInfo.fetalImagingReportImgPath"
                  :fileType="uploadType"
                  :disabled="detailDisable"
                />
              </template>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="display-flex">
              <el-form-item label="其他检测报告：">
                <el-radio-group v-model="nsdOtherInfo.otherTestReport" :disabled="detailDisable" class="mr20">
                  <el-radio :label="0">无</el-radio>
                  <el-radio :label="1">有</el-radio>
                </el-radio-group>
              </el-form-item>
              <template v-if="nsdOtherInfo.otherTestReport === 1">
                <file-upload
                  v-model="nsdOtherInfo.otherTestReportImgPath"
                  :fileType="uploadType"
                  :disabled="detailDisable"
                />
              </template>
            </div>
          </el-col>
          <el-col :span="24">
            <el-form-item label="样本状态:">
              <el-radio-group v-model="nsdOtherInfo.sampleStatus" :disabled="detailDisable" class="mr20">
                <el-radio :label="1">检测中</el-radio>
                <el-radio :label="2">已完成</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检测结果:">
              <div>{{ nsdOtherInfo.reportResult}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="信息采集人员:">
              <el-col :span="5">
                <el-input
                  v-model="nsdOtherInfo.collector"
                  :disabled="detailDisable"
                  placeholder="请输入信息采集人员信息"
                ></el-input
              ></el-col>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="信息采集日期:">
              <el-col :span="5">
                <el-date-picker
                  clearable
                  v-model="nsdOtherInfo.collectionDate"
                  type="date"
                  :disabled="detailDisable"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择时间"
                >
                </el-date-picker>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="go-back-view">
      <el-button type="primary" plain @click="goBack">返回</el-button>
      <el-button v-if="!detailDisable" type="primary" @click="submitForm">保存</el-button>
    </div>
  </div>
</template>

<script>
import { updateNITP } from "@/api/caseManagement";

import FileUpload from "@/components/FileUpload";
import GlobalConstants from "@/constants.js";
import cloneDeep from "lodash/cloneDeep";

export default {
  props: {
    patientData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    patientTypeMap: {
      type: Object,
      default: () => {
        return {};
      },
    },
    uploadType: {
      type: Array,
      default: () => {
        return [];
      },
    },
    routerType: {
      type: String,
      default: "",
    },
  },
  watch: {
    patientData: {
      handler(newValue) {
        this.info = cloneDeep(newValue);
        const nsdBaseInfo = newValue.nsdCaseInfoVO?.nsdBaseInfo ?? {};
        const nsdAncillaryExamination = newValue.nsdCaseInfoVO?.nsdAncillaryExamination ?? {};
        const nsdMedicalHistory = newValue.nsdCaseInfoVO?.nsdMedicalHistory ?? {};
        const nsdOtherInfo = newValue.nsdCaseInfoVO?.nsdOtherInfo ?? {};
        console.log(nsdBaseInfo);

        // const santiNIPTData = {
        //   assanti21: false,
        //   assanti18: false,
        //   assanti13: false,
        // }
        // const santiCNVData = {
        //   assanti21: false,
        //   assanti18: false,
        //   assanti13: false,
        // }
        // if (Object.keys(formNIPTInfo).length > 0) {
        //   if (!formNIPTInfo.santiType) {
        //     formNIPTInfo.santiType = ''
        //   }
        //   if (formNIPTInfo.santiType.length > 0) {
        //     const arr = formNIPTInfo.santiType.split(',')
        //     arr.forEach(ele => {
        //       santiNIPTData[`assanti${ele}`] = true
        //     })
        //   }
        // }
        // if (Object.keys(formCNVInfo).length > 0) {
        //   if (!formCNVInfo.santiType) {
        //     formCNVInfo.santiType = ''
        //   }
        //   if (formCNVInfo.santiType.length > 0) {
        //     const arr = formCNVInfo.santiType.split(',')
        //     arr.forEach(ele => {
        //       santiCNVData[`assanti${ele}`] = true
        //     })
        //   }
        // }
        console.log("nsdBaseInfo.bloodCollectionTime", nsdBaseInfo.bloodCollectionTime);
        this.nsdBaseInfo = {
          ...this.nsdBaseInfo,
          ...nsdBaseInfo,
        };
        this.nsdMedicalHistory = {
          ...this.nsdMedicalHistory,
          ...nsdMedicalHistory,
          currentDiseases: nsdMedicalHistory.currentDiseases?.split?.(",") || [],
        };
        // 测定周天，需手动换算，接口存天。
        this.nsdAncillaryExamination = {
          ...this.nsdAncillaryExamination,
          ...nsdAncillaryExamination,
          ntCheckGestationalWeeks: Math.floor((nsdAncillaryExamination.ntCheckGestationalWeeks || 0) / 7),
          ntCheckGestationalDay: (nsdAncillaryExamination.ntCheckGestationalWeeks || 0) % 7,
        };
        this.nsdOtherInfo = {
          ...this.nsdOtherInfo,
          ...nsdOtherInfo,
        };
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      educationLevelOptions: GlobalConstants.EDUCATION_LEVEL_OPTION,
      diseaseOptions: GlobalConstants.DISEASE_TYPE_OPTION,
      nsdBaseInfo: {
        menstrualRegular: true,
      },
      info:{},
      nsdMedicalHistory: {
        adversePregnancy: 0,
        currentDiseases: [],
        bloodTransfusionHistory: 0,
        transplantationHistory: 0,
        allogeneicCellTherapyHistory: 0,
        stemCellTherapyHistory: 0,
        cancerHistoryHistory: 0,
        personalGeneticHistory: 0,
        familyGeneticHistory: 0,
        singleGeneHistory: 0,
        geneticTesting: 0,
        consanguinityHistory: 0,
      },
      nsdAncillaryExamination: {},
      nsdOtherInfo: {},
      formRules: {
        // structureText: [{ required: true, validator: structureTextVal, trigger: "change" }],
        // indicatorRiskText: [{ required: true, validator: indicatorRiskTextVal, trigger: "change" }],
        // surgicalTreatmentText: [{ required: true, validator: surgicalTreatmentTextVal, trigger: "change" }],
      },
      baseInfos: [
        { label: "姓名", valueKey: ["gname", "gName"] },
        { label: "年龄", valueKey: ["gage", "gAge"] },
        { label: "证件类型", valueKey: ["docType"] },
        { label: "证件号码", valueKey: ["idNum"] },
        { label: "联系方式", valueKey: ["gmobile", "gMobile"] },
        { label: "末次月经时间", valueKey: ["menstruationEndTime"] },
      ],
    };
  },
  computed: {
    detailDisable() {
      return this.routerType !== "supplement";
    },
  },
  methods: {
    submitForm() {
      Promise.all([
        this.formValidate("baseForm"),
        this.formValidate("medicalHistoryForm"),
        this.formValidate("nsdAuxiliaryExaminationsForm"),
        this.formValidate("nsdOtherInfoForm"),
      ])
        .then(() => {
          const params = {
            patientId: this.patientData.id,
            pcode: this.patientData.pCode,
            userId: this.patientData.userId,
            nsdBaseInfo: this.nsdBaseInfo,
            nsdMedicalHistory: {
              ...this.nsdMedicalHistory,
              currentDiseases: this.nsdMedicalHistory.currentDiseases?.join?.(","),
            },
            nsdAncillaryExamination: {
              ...this.nsdAncillaryExamination,
              ntCheckGestationalWeeks:
                (this.nsdAncillaryExamination.ntCheckGestationalWeeks || 0) * 7 +
                (this.nsdAncillaryExamination.ntCheckGestationalDay || 0),
            },
            nsdOtherInfo: this.nsdOtherInfo,
          };
          console.log(params);
       
          updateNITP(params).then((res) => {
            this.$modal.msgSuccess("修改成功");
            setTimeout(() => {
              this.goBack();
            }, 1000);
          });
        })
        .catch(() => {
          this.$modal.msgError("请完善病历信息");
        });
    },
    formValidate(formName) {
      return new Promise((resolve, reject) => {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            resolve();
          } else {
            reject();
          }
        });
      });
    },
    goBack() {
      const obj = { path: "/caseManagement/case_list" };
      this.$tab.closeOpenPage(obj);
    },
  },
};
</script>
