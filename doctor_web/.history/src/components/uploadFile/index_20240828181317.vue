<template>
    <el-upload
      action="#"
      :file-list="value"
      :before-upload="beforeUpload"
     
      list-type="text"
    >
      <el-button size="small" type="primary">点击上传</el-button>
      <template #file="{ file }">
        <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
          <span>{{ file.newFilename }}</span>
          <div>
            <el-button type="text" @click="handleDownload(file)">下载</el-button>
            <el-button type="text" @click="handleRemoveFile(file)">删除</el-button>
          </div>
        </div>
      </template>
    </el-upload>
  </template>
  
  <script>
  import { uploadFile,handleRemove,handlelist } from '@/api/caseManagement';
  
  export default {
    props: {
      value: {
        type: Array,
        default: () => [],
      },
      pCode: {
        type: String,
        default: '',
      },
      fileType: {
        type: String,
        default: '',
      },
    },
    data(){
        return{

        }
// return {
//     value:[]
// }
    } ,
    methods: {
    //   changeValue(val) {
    //     let value = [...this.value];
    //     value.push(val);
    //     this.$emit('input', value);
    //     // this.$emit('changeList', value);
    //     this.$emit('onSuccess');
    //   },
      beforeUpload(file) {
        let formData = new FormData();
        formData.append('file', file);
        formData.append('pCode', this.pCode);
        formData.append('fileType', this.fileType);
        uploadFile(formData).then(res => {
          if (res.code === 200) {
            this.$message.success('上传成功');
            // this.changeValue({
            //   name: file.newFilename,
            //   url: res.data,
            //   fileName:res.fileName
            // });
          } else {
            this.$message.error(res.message || '操作失败');
          }
        });
        return false;
      },
      handleRemoveFile(file) {
        console.log(file);
        let formData = new FormData();
        formData.append('filePath', file.fileName);
        formData.append('pCode', this.pCode);
        formData.append('fileType', this.fileType);
        handleRemove(formData).then(res => {
        if (res.code === 200) {
          this.$message.success('删除成功');
          const index = this.value.findIndex(f => f.newFilename === file.newFilename);
          if (index !== -1) {
            const newList = [...this.value];
            newList.splice(index, 1);
            this.$emit('input', newList);
            this.$emit('onSuccess');
            // this.$emit('changeList', newList);
          }
        } else {
          this.$message.error(res.message || '删除失败');
        }
      });
      },
    //   handleDownload(file) {
    //     // window.open(file.url, '_blank');
    //     this.download('system/common/download', {
    //     ...this.queryParams
    //   }, ``)
    //   },
     
    },
  };
  </script>
  
  <style lang="less" scoped></style>
  