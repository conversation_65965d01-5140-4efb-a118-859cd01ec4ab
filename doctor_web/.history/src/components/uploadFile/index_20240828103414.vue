<template>

        <el-upload action="#" :file-list="value" :before-upload="beforeUpload" :on-remove="handleRemove" >
        <el-button  size="small" type="primary"   >点击上传</el-button>
        <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
    </el-upload>


</template>

<script>
import api from '@/assets/scripts/common.js';
export default {
    props: {
        value: {
            type: Array,
            default: () => []
        },
       

    },
    data() {
        return {


        };
    },
    methods: {

        handleRemove(...args){
            this.$emit('input', args[1]);
            this.$emit('changeList', args[1]);
        },
        changeValue(val) {
            let value = [...this.value];
            value.push(val);
            this.$emit('input', value);
            this.$emit('changeList', value);
        },
        beforeUpload(file) {

            let formData = new FormData();
            formData.append('files', file);
            api.uploadFile(formData).then(res => {
                if (res.code === 200) {

                    this.$message.success("上传成功");
                    this.changeValue({
                        name: file.name,
                        url: res.data


                    });
                } else {

                    this.$message.error(res.message || "操作失败");
                }
            })
            return false;
        }
    }
};
</script>

<style lang="less" scoped></style>
