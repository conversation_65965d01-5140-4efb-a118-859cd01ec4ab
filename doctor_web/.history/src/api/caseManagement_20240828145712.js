import request from '@/utils/request'

// 获取当前医生的信息
export function getInfoDoctorClient() {
  return request({
    url: '/doctorClient/getInfo',
    method: 'post'
  })
}

// 查询病历列表
export function listDoctorClient(query) {
  return request({
    url: '/doctorClient/pageBean',
    method: 'get',
    params: query
  })
}

// 查询病历详细
export function getDoctorClientPatient(id) {
  return request({
    url: '/doctorClient/patient/' + id,
    method: 'get'
  })
}

// 携带者筛查新增
// export function addClinicalinformation(data) {
//   return request({
//     url: '/doctorClient/clinicalinformation',
//     method: 'post',
//     data: data
//   })
// }

// 携带者筛查更新
export function updateClinicalinformation(data) {
  return request({
    url: '/doctorClient/clinicalinformation',
    method: 'put',
    data: data
  })
}

// 携带者检测报告  caseId=24SWCS0000
export function getServiceCSData(id) {
  return request({
    url: '/service/getCSData?pCode=' + id,
    method: 'get'
  })
}

// 孕妇相关信息新增
// export function addGravidainfo(data) {
//   return request({
//     url: '/doctorClient/gravidainfo',
//     method: 'post',
//     data: data
//   })
// }

// 孕妇相关信息更新
export function updateGravidainfo(data) {
  return request({
    url: '/doctorClient/gravidainfo',
    method: 'put',
    data: data
  })
}

// 新生儿相关信息新增
// export function addNeonate(data) {
//   return request({
//     url: '/doctorClient/neonate',
//     method: 'post',
//     data: data
//   })
// }

// 新生儿关信息更新
export function updateNeonate(data) {
  return request({
    url: '/doctorClient/neonate',
    method: 'put',
    data: data
  })
}


// 无创单病信息更新
export function updateNITP(data) {
  return request({
    url: '/doctorClient/nsd',
    method: 'put',
    data: data
  })
}

// 转诊记录分页条件查找
export function listPatientreferral(query) {
  return request({
    url: '/doctorClient/patientreferral',
    method: 'get',
    params: query
  })
}

// 转诊记录查找
export function getPatientreferral(id) {
  return request({
    url: '/doctorClient/patientreferral/' + id,
    method: 'get'
  })
}

// 转诊记录新增
export function addPatientreferral(data) {
  return request({
    url: '/doctorClient/patientreferral',
    method: 'post',
    data: data
  })
}

// 转诊记录更新
export function updatePatientreferral(data) {
  return request({
    url: '/doctorClient/patientreferral',
    method: 'put',
    data: data
  })
}

// 转诊记录删除
export function delPatientreferral(id) {
  return request({
    url: '/doctorClient/patientreferral/' + id,
    method: 'delete'
  })
}

// 患者随访计划分页条件查找
export function listPatientvisitor(query) {
  return request({
    url: '/doctorClient/patientvisitor',
    method: 'get',
    params: query
  })
}

// 患者随访计划查找
export function getPatientvisitor(id) {
  return request({
    url: '/doctorClient/patientvisitor/' + id,
    method: 'get'
  })
}

// 患者随访计划新增
export function addPatientvisitor(data) {
  return request({
    url: '/doctorClient/patientvisitor',
    method: 'post',
    data: data
  })
}

// 患者随访计划更新
export function updatePatientvisitor(data) {
  return request({
    url: '/doctorClient/patientvisitor',
    method: 'put',
    data: data
  })
}

// 患者随访计划删除
export function delPatientvisitor(id) {
  return request({
    url: '/doctorClient/patientvisitor/' + id,
    method: 'delete'
  })
}

//推送到小程序
export function pushReportToWx(data) {
  return request({
    url: '/doctorClient/pushReportToWx',
    method: 'post',
    data: data
  })
}

export function uploadFile(data = {}) {
  return request({
      url: '/recordsFile/uploadRecordsFile',
      method: "post",
      data,
      headers: {
          "Content-Type": "multipart/form-data",
      }
  });

}
//删除文件
export function handleRemove(data = {}) {
  return request({
      url: '/recordsFile/removeRecordsFile',
      method: "post",
      data,
      headers: {
          "Content-Type": "multipart/form-data",
      }
  });

}