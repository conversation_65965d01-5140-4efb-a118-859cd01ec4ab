import request from '@/utils/request'

// 组织机构树形结构-不分页
export function listOrgan(query) {
  return request({
    url: '/organ',
    method: 'get',
    params: query
  })
}

// 组织机构树形结构
export function listOrganPageBean(query) {
  return request({
    url: '/organ/pageBean',
    method: 'get',
    params: query
  })
}

// 当前用户的组织机构树形结构-不分页
export function listOrganUser(query) {
  return request({
    url: '/organ/user',
    method: 'get',
    params: query
  })
}

// 查询组织机构详细
export function getOrgan(id) {
  return request({
    url: '/organ/' + id,
    method: 'get'
  })
}

// 新增组织机构
export function addOrgan(data) {
  return request({
    url: '/organ',
    method: 'post',
    data: data
  })
}

// 修改组织机构
export function updateOrgan(data) {
  return request({
    url: '/organ',
    method: 'put',
    data: data
  })
}

// 删除组织机构
export function delOrgan(id) {
  return request({
    url: '/organ/' + id,
    method: 'delete'
  })
}
