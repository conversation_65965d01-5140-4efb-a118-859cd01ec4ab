import request from '@/utils/request';

// 查询疾病种类模板管理列表
export function listTemplate(query) {
    return request({
        url: '/diseasesTypeTemplate/list',
        method: 'get',
        params: query,
    });
}

// 查询疾病种类模板管理详细
export function getTemplate(id) {
    return request({
        url: '/diseasesTypeTemplate/' + id,
        method: 'get',
    });
}

// 新增疾病种类模板管理
export function addTemplate(data) {
    return request({
        url: '/diseasesTypeTemplate',
        method: 'post',
        data: data,
    });
}

// 修改疾病种类模板管理
export function updateTemplate(data) {
    return request({
        url: '/diseasesTypeTemplate',
        method: 'put',
        data: data,
    });
}

// 删除疾病种类模板管理
export function delTemplate(id) {
    return request({
        url: '/diseasesTypeTemplate/' + id,
        method: 'delete',
    });
}
