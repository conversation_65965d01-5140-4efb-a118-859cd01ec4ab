import request from '@/utils/request'

// 查询医院管理列表
export function listHospital(query) {
  return request({
    url: '/hospital',
    method: 'get',
    params: query
  })
}

// 查询医院管理详细
export function getHospital(id) {
  return request({
    url: '/hospital/' + id,
    method: 'get'
  })
}

// 新增医院管理
export function addHospital(data) {
  return request({
    url: '/hospital',
    method: 'post',
    data: data
  })
}

// 修改医院管理
export function updateHospital(data) {
  return request({
    url: '/hospital',
    method: 'put',
    data: data
  })
}

// 删除医院管理
export function delHospital(id) {
  return request({
    url: '/hospital/' + id,
    method: 'delete'
  })
}
