import request from '@/utils/request';

// 查询轮播图信息列表
export function listPicture(query) {
    return request({
        url: '/picture/list',
        method: 'get',
        params: query,
    });
}

// 查询轮播图信息详细
export function getPicture(id) {
    return request({
        url: '/picture/' + id,
        method: 'get',
    });
}

// 新增轮播图信息
export function addPicture(data) {
    return request({
        url: '/picture',
        method: 'post',
        data: data,
    });
}

// 修改轮播图信息
export function updatePicture(data) {
    return request({
        url: '/picture',
        method: 'put',
        data: data,
    });
}

// 删除轮播图信息
export function delPicture(id) {
    return request({
        url: '/picture/' + id,
        method: 'delete',
    });
}
