import request from '@/utils/request';

// 查询疾病种类管理列表
export function listType(query) {
    return request({
        url: '/diseasesType/list',
        method: 'get',
        params: query,
    });
}

// 查询疾病种类管理详细
export function getType(id) {
    return request({
        url: '/diseasesType/' + id,
        method: 'get',
    });
}

// 新增疾病种类管理
export function addType(data) {
    return request({
        url: '/diseasesType/add',
        method: 'post',
        data: data,
    });
}

// 修改疾病种类管理
export function updateType(data) {
    return request({
        url: '/diseasesType/updateById',
        method: 'put',
        data: data,
    });
}

// 删除疾病种类管理
export function delType(id) {
    return request({
        url: '/diseasesType/' + id,
        method: 'delete',
    });
}
