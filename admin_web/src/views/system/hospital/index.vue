<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="医院编号" prop="hCode">
        <el-input
          v-model="queryParams.hCode"
          placeholder="请输入医院编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="医院名称" prop="hospName">
        <el-input
          v-model="queryParams.hospName"
          placeholder="请输入医院名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医院等级" prop="gradeName">
        <el-input
          v-model="queryParams.gradeName"
          placeholder="请输入医院等级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="医院等级枚举" prop="gradeCode">
        <el-input
          v-model="queryParams.gradeCode"
          placeholder="请输入医院等级枚举"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="性质名称" prop="natureName">
        <el-input
          v-model="queryParams.natureName"
          placeholder="请输入性质名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="性质名称枚举" prop="natureCode">
        <el-input
          v-model="queryParams.natureCode"
          placeholder="请输入性质名称枚举"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="组织机构id" prop="orgId">
        <el-input
          v-model="queryParams.orgId"
          placeholder="请输入组织机构id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="组织机构编号" prop="orgCode">
        <el-input
          v-model="queryParams.orgCode"
          placeholder="请输入组织机构编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="服务热线" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入服务热线"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="地址名称" prop="addressName">
        <el-input
          v-model="queryParams.addressName"
          placeholder="请输入地址名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="${comment}" prop="createUser">
        <el-input
          v-model="queryParams.createUser"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="${comment}" prop="updataUser">
        <el-input
          v-model="queryParams.updataUser"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="创建时间" prop="beginTime">
        <el-date-picker clearable
          v-model="dateRange"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="${comment}" prop="updataTime">
        <el-date-picker clearable
          v-model="queryParams.updataTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择${comment}">
        </el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="${comment}" prop="deleted">
        <el-input
          v-model="queryParams.deleted"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="${comment}" prop="sort">
        <el-input
          v-model="queryParams.sort"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:hospital:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:hospital:edit']"
        >修改</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:hospital:remove']"
        >删除</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:hospital:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="hospitalList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
      <!-- <el-table-column label="医院编号" align="center" prop="hCode" /> -->
      <el-table-column label="医院名称" align="center" prop="hospName" />
      <el-table-column label="医院地址" align="center" prop="addressName" />
      <el-table-column label="医院等级" align="center" prop="gradeName" />
      <!-- <el-table-column label="医院等级枚举" align="center" prop="gradeCode" /> -->
      <el-table-column label="医院性质" align="center" prop="natureName" />
      <!-- <el-table-column label="性质名称枚举" align="center" prop="natureCode" /> -->
      <!-- <el-table-column label="组织机构id" align="center" prop="orgId" />
      <el-table-column label="组织机构编号" align="center" prop="orgCode" /> -->
      <!-- <el-table-column label="服务热线" align="center" prop="mobile" /> -->
      <!-- <el-table-column label="${comment}" align="center" prop="createUser" />
      <el-table-column label="${comment}" align="center" prop="updataUser" /> -->
      <!-- <el-table-column label="${comment}" align="center" prop="updataTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updataTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="已有医生" align="center" prop="doctorSvm" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <!-- <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span> -->
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="${comment}" align="center" prop="deleted" />
      <el-table-column label="${comment}" align="center" prop="status" />
      <el-table-column label="${comment}" align="center" prop="sort" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDetail(scope.row)"
            v-hasPermi="['system:hospital:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:hospital:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:hospital:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看医院信息 -->
    <el-dialog title="医院信息" :visible.sync="openHospitalDetail" width="500px" append-to-body>
      <el-row :gutter="20" style="line-height: 36px;">
        <el-col :span="12">
          <span>医院名称: </span>
          <span>{{ selectHospitalData.hospName }}</span>
        </el-col>
        <el-col :span="12">
          <span>医院等级: </span>
          <span>{{ selectHospitalData.gradeName }}</span>
        </el-col>
        <el-col :span="12">
          <span>组织: </span>
          <span>{{ selectHospitalData.orgName }}</span>
        </el-col>
        <el-col :span="12">
          <span>医院性质: </span>
          <span>{{ selectHospitalData.natureName }}</span>
        </el-col>
        <el-col :span="12">
          <span>已有医生: </span>
          <span>{{ selectHospitalData.doctorSvm }}</span>
        </el-col>
      </el-row>
    </el-dialog>

    <!-- 添加或修改医院管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="医院编号" prop="hCode">
          <el-input v-model="form.hCode" placeholder="请输入医院编号" />
        </el-form-item> -->
        <el-form-item label="医院名称" prop="hospName">
          <el-input v-model="form.hospName" placeholder="请输入医院名称" />
        </el-form-item>
        <el-form-item label="医院等级" prop="gradeName">
          <el-input v-model="form.gradeName" placeholder="请输入医院等级" />
        </el-form-item>
        <!-- <el-form-item label="医院等级枚举" prop="gradeCode">
          <el-input v-model="form.gradeCode" placeholder="请输入医院等级枚举" />
        </el-form-item> -->
        <el-form-item label="医院性质" prop="natureName">
          <el-input v-model="form.natureName" placeholder="请输入医院性质" />
        </el-form-item>
        <!-- <el-form-item label="性质名称枚举" prop="natureCode">
          <el-input v-model="form.natureCode" placeholder="请输入性质名称枚举" />
        </el-form-item> -->
        <el-form-item label="选择组织" prop="orgId">
          <treeselect v-model="form.orgId" :options="organList" :show-count="true" placeholder="请选择组织" />
        </el-form-item>
        <!-- <el-form-item label="组织机构id" prop="orgId">
          <el-input v-model="form.orgId" placeholder="请输入组织机构id" />
        </el-form-item>
        <el-form-item label="组织机构编号" prop="orgCode">
          <el-input v-model="form.orgCode" placeholder="请输入组织机构编号" />
        </el-form-item> -->
        <el-form-item label="服务热线">
          <el-input v-model="form.mobile" placeholder="请输入服务热线" />
        </el-form-item>
        <el-form-item label="医院地址" prop="addressName">
          <el-input v-model="form.addressName" placeholder="请输入医院地址" />
        </el-form-item>
        <!-- <el-form-item label="${comment}" prop="createUser">
          <el-input v-model="form.createUser" placeholder="请输入${comment}" />
        </el-form-item> -->
        <!-- <el-form-item label="${comment}" prop="updataUser">
          <el-input v-model="form.updataUser" placeholder="请输入${comment}" />
        </el-form-item> -->
        <!-- <el-form-item label="${comment}" prop="updataTime">
          <el-date-picker clearable
            v-model="form.updataTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择${comment}">
          </el-date-picker>
        </el-form-item> -->
        <!-- <el-form-item label="${comment}" prop="deleted">
          <el-input v-model="form.deleted" placeholder="请输入${comment}" />
        </el-form-item> -->
        <!-- <el-form-item label="${comment}" prop="sort">
          <el-input v-model="form.sort" placeholder="请输入${comment}" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listHospital, getHospital, delHospital, addHospital, updateHospital } from "@/api/system/hospital";
import { listOrgan } from "@/api/system/organ";

import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Hospital",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      dateRange: [],
      // 医院管理表格数据
      hospitalList: [],
      // 组织列表
      organList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查看医院信息弹窗
      openHospitalDetail: false,
      // 查看、编辑的医院内容
      selectHospitalData: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        hCode: null,
        hospName: null,
        gradeName: null,
        gradeCode: null,
        natureName: null,
        natureCode: null,
        orgId: null,
        orgCode: null,
        mobile: null,
        addressName: null,
        createUser: null,
        updataUser: null,
        updataTime: null,
        beginTime: null,
        deleted: null,
        status: null,
        sort: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        hospName: [
          { required: true, message: "请输入医院名称", trigger: "blur" },
          { min: 1, max: 30, message: '医院名称长度最大不能超过 30 个字符', trigger: 'blur' }
        ],
        gradeName: [
          { required: true, message: "请输入医院等级", trigger: "blur" },
          { min: 1, max: 30, message: '医院等级长度最大不能超过 30 个字符', trigger: 'blur' }
        ],
        natureName: [
          { required: true, message: "请输入医院性质", trigger: "blur" },
          { min: 1, max: 30, message: '医院性质长度最大不能超过 30 个字符', trigger: 'blur' }
        ],
        orgId: [
          { required: true, message: "请选择组织", trigger: "blur" }
        ],
        // mobile: [
        //   { required: true, message: "请输入服务热线", trigger: "blur" }
        // ],
        addressName: [
          { required: true, message: "请输入医院地址", trigger: "blur" },
          { min: 1, max: 100, message: '医院地址长度最大不能超过 100 个字符', trigger: 'blur' }
        ],
      }
    };
  },
  watch: {
    $route: {
      handler(newValue) {
        if (newValue.name === 'Hospital' || newValue.path === '/hospital/hospital') {
          this.getList();
          this.getOrganList();
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    // this.getList();
    // this.getOrganList();
  },
  methods: {
    getOrganList() {
      listOrgan(this.queryParams).then(response => {
        // this.organList = response.data.list
        const arr = []
        response.data.list.forEach(ele => {
          let chidC = {
            id: ele.id,
            label: ele.orgName
          }
          if (ele.childList.length > 0) {
            const chidData = []
            ele.childList.forEach(chiItem => {
              let chiItemC = {
                id: chiItem.id,
                label: chiItem.orgName
              }
              if (chiItem.childList.length > 0) {
                let chiItemData = []
                chiItemData = chiItem.childList.map(tochi => {
                  return {
                    id: tochi.id,
                    label: tochi.orgName
                  }
                })
                chiItemC = { ...chiItemC, ...{ children: chiItemData }}
              }
              chidData.push(chiItemC)
            })
            chidC = { ...chidC, ...{ children: chidData }}
          }
          arr.push(chidC)
        })
        this.organList = arr
      });
    },
    /** 查询医院管理列表 */
    getList() {
      this.loading = true;
      const params = JSON.parse(JSON.stringify(this.queryParams))
      if (this.dateRange && this.dateRange.length > 0) {
        params['beginTime'] = `${this.dateRange[0]} 00:00:00`
        params['endTime'] = `${this.dateRange[1]} 23:59:59`
      }
      listHospital(params).then(response => {
        this.hospitalList = response.data.list || [];
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        hCode: null,
        hospName: null,
        gradeName: null,
        gradeCode: null,
        natureName: null,
        natureCode: null,
        orgId: null,
        orgCode: null,
        mobile: null,
        addressName: null,
        createUser: null,
        createTime: null,
        updataUser: null,
        updataTime: null,
        deleted: null,
        status: null,
        sort: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加医院管理";
    },
    /** 查看按钮操作 */
    handleDetail(row) {
      const id = row.id || this.ids
      getHospital(id).then(response => {
        this.selectHospitalData = response.data
        this.openHospitalDetail = true
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getHospital(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改医院管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateHospital(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHospital(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除当前数据？').then(function() {
        return delHospital(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/hospital/export', {
        ...this.queryParams
      }, `hospital_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
