<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="110px">
            <el-form-item label="疾病种类id" prop="dtId">
                <el-input v-model="queryParams.dtId" placeholder="请输入疾病种类id" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="疾病种类模板名称" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入疾病种类模板名称" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:template:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:template:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:template:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:template:export']">导出</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="templateList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="主键ID" align="center" prop="id" />
            <el-table-column label="疾病种类id" align="center" prop="dtId" />
            <el-table-column label="疾病种类模板名称" align="center" prop="name" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="状态1-启用 0-停用" align="center" prop="status" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:template:edit']">修改</el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:template:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改疾病种类模板管理对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="疾病种类id" prop="dtId">
                    <el-input v-model="form.dtId" placeholder="请输入疾病种类id" />
                </el-form-item>
                <el-form-item label="疾病种类模板名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入疾病种类模板名称" />
                </el-form-item>
                <el-form-item label="备注" prop="remarks">
                    <el-input v-model="form.remarks" placeholder="请输入备注" />
                </el-form-item>
                <el-form-item label="创建用户" prop="createUser">
                    <el-input v-model="form.createUser" placeholder="请输入创建用户" />
                </el-form-item>
                <el-form-item label="更新用户" prop="updataUser">
                    <el-input v-model="form.updataUser" placeholder="请输入更新用户" />
                </el-form-item>
                <el-form-item label="更新时间" prop="updataTime">
                    <el-date-picker clearable v-model="form.updataTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择更新时间"> </el-date-picker>
                </el-form-item>
                <el-form-item label="删除标识" prop="deleted">
                    <el-input v-model="form.deleted" placeholder="请输入删除标识" />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listTemplate, getTemplate, delTemplate, addTemplate, updateTemplate } from '@/api/system/template';

export default {
    name: 'Template',
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 疾病种类模板管理表格数据
            templateList: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                dtId: null,
                name: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                dtId: [{ required: true, message: '疾病种类id不能为空', trigger: 'blur' }],
                name: [{ required: true, message: '疾病种类模板名称不能为空', trigger: 'blur' }],
                createTime: [{ required: true, message: '创建时间不能为空', trigger: 'blur' }],
                status: [{ required: true, message: '状态1-启用 0-停用不能为空', trigger: 'change' }],
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询疾病种类模板管理列表 */
        getList() {
            this.loading = true;
            listTemplate(this.queryParams).then((response) => {
                this.templateList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                dtId: null,
                name: null,
                remarks: null,
                createUser: null,
                createTime: null,
                updataUser: null,
                updataTime: null,
                deleted: null,
                status: null,
            };
            this.resetForm('form');
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = '添加疾病种类模板管理';
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids;
            getTemplate(id).then((response) => {
                this.form = response.data;
                this.open = true;
                this.title = '修改疾病种类模板管理';
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.id != null) {
                        updateTemplate(this.form).then((response) => {
                            this.$modal.msgSuccess('修改成功');
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addTemplate(this.form).then((response) => {
                            this.$modal.msgSuccess('新增成功');
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal
                .confirm('是否确认删除疾病种类模板管理编号为"' + ids + '"的数据项？')
                .then(function () {
                    return delTemplate(ids);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download(
                'system/template/export',
                {
                    ...this.queryParams,
                },
                `template_${new Date().getTime()}.xlsx`
            );
        },
    },
};
</script>
