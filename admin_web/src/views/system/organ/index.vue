<template>
  <div class="app-container">
    <!-- <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px"> -->
      <!-- <el-form-item label="组织名称" prop="orgName">
        <el-input
          v-model="queryParams.orgName"
          placeholder="请输入组织名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="组织编号" prop="orgCode">
        <el-input
          v-model="queryParams.orgCode"
          placeholder="请输入组织编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="当前几级" prop="levelNum">
        <el-input
          v-model="queryParams.levelNum"
          placeholder="请输入当前几级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="组织描述" prop="orgDescribe">
        <el-input
          v-model="queryParams.orgDescribe"
          placeholder="请输入描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="父id" prop="pid">
        <el-input
          v-model="queryParams.pid"
          placeholder="请输入父id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="一级" prop="levelOne">
        <el-input
          v-model="queryParams.levelOne"
          placeholder="请输入一级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="二级" prop="levelTwo">
        <el-input
          v-model="queryParams.levelTwo"
          placeholder="请输入二级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="三级" prop="levelThree">
        <el-input
          v-model="queryParams.levelThree"
          placeholder="请输入三级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="${comment}" prop="createUser">
        <el-input
          v-model="queryParams.createUser"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="${comment}" prop="updataTime">
        <el-date-picker clearable
          v-model="queryParams.updataTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择${comment}">
        </el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="${comment}" prop="updataUser">
        <el-input
          v-model="queryParams.updataUser"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="${comment}" prop="deleted">
        <el-input
          v-model="queryParams.deleted"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="排序" prop="sort">
        <el-input
          v-model="queryParams.sort"
          placeholder="请输入排序"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form> -->

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:organ:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:organ:edit']"
        >修改</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:organ:remove']"
        >删除</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:organ:export']"
        >导出</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="toggleExpandAll"
        >展开/折叠</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="organList"
      row-key="id"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'childList', hasChildren: 'hasChildren'}"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
      <el-table-column label="组织名称" prop="orgName" />
      <el-table-column label="描述" align="center" prop="orgDescribe" />
      <!-- <el-table-column label="组织编号" align="center" prop="orgCode" /> -->
      <!-- <el-table-column label="当前几级" align="center" prop="levelNum" /> -->
      <!-- <el-table-column label="父id" align="center" prop="pid" /> -->
      <!-- <el-table-column label="一级" align="center" prop="levelOne" /> -->
      <!-- <el-table-column label="二级" align="center" prop="levelTwo" /> -->
      <!-- <el-table-column label="三级" align="center" prop="levelThree" /> -->
      <!-- <el-table-column label="${comment}" align="center" prop="createUser" /> -->
      <!-- <el-table-column label="${comment}" align="center" prop="updataTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updataTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="${comment}" align="center" prop="updataUser" /> -->
      <!-- <el-table-column label="${comment}" align="center" prop="deleted" /> -->
      <!-- <el-table-column label="${comment}" align="center" prop="status" /> -->
      <!-- <el-table-column label="排序" align="center" prop="sort" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.levelNum <= 2"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleAdd(scope.row)"
            v-hasPermi="['system:organ:add']"
          >新增</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:organ:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:organ:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改组织机构对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="组织名称" prop="orgName">
          <el-input v-model="form.orgName" placeholder="请输入组织名称" />
        </el-form-item>
        <el-form-item label="描述" prop="orgDescribe">
          <el-input v-model="form.orgDescribe" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <!-- <el-form-item label="组织编号" prop="orgCode">
          <el-input v-model="form.orgCode" placeholder="请输入组织编号" />
        </el-form-item> -->
        <!-- <el-form-item label="当前几级" prop="levelNum">
          <el-input v-model="form.levelNum" placeholder="请输入当前几级" />
        </el-form-item> -->
        <!-- <el-form-item label="父id" prop="pid">
          <el-input v-model="form.pid" placeholder="请输入父id" />
        </el-form-item> -->
        <!-- <el-form-item label="一级" prop="levelOne">
          <el-input v-model="form.levelOne" placeholder="请输入一级" />
        </el-form-item> -->
        <!-- <el-form-item label="二级" prop="levelTwo">
          <el-input v-model="form.levelTwo" placeholder="请输入二级" />
        </el-form-item> -->
        <!-- <el-form-item label="三级" prop="levelThree">
          <el-input v-model="form.levelThree" placeholder="请输入三级" />
        </el-form-item> -->
        <!-- <el-form-item label="${comment}" prop="createUser">
          <el-input v-model="form.createUser" placeholder="请输入${comment}" />
        </el-form-item> -->
        <!-- <el-form-item label="${comment}" prop="updataTime">
          <el-date-picker clearable
            v-model="form.updataTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择${comment}">
          </el-date-picker>
        </el-form-item> -->
        <!-- <el-form-item label="${comment}" prop="updataUser">
          <el-input v-model="form.updataUser" placeholder="请输入${comment}" />
        </el-form-item> -->
        <!-- <el-form-item label="${comment}" prop="deleted">
          <el-input v-model="form.deleted" placeholder="请输入${comment}" />
        </el-form-item> -->
        <!-- <el-form-item label="排序" prop="sort">
          <el-input v-model="form.sort" placeholder="请输入排序" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrganPageBean, getOrgan, delOrgan, addOrgan, updateOrgan } from "@/api/system/organ";

export default {
  name: "Organ",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 组织机构表格数据
      organList: [],
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orgName: null,
        orgDescribe: null,
        orgDescribe: null,
        orgCode: null,
        levelNum: null,
        pid: null,
        levelOne: null,
        levelTwo: null,
        levelThree: null,
        createUser: null,
        updataTime: null,
        updataUser: null,
        deleted: null,
        status: null,
        sort: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // levelNum: [
        //   { required: true, message: "当前几级不能为空", trigger: "blur" }
        // ],
        // pid: [
        //   { required: true, message: "父id不能为空", trigger: "blur" }
        // ],
        orgName: [
          { required: true, message: "请输入组织名称", trigger: "blur" },
          { min: 1, max: 30, message: '组织名称长度最大不能超过 30 个字符', trigger: 'blur' }
        ],
        orgDescribe: [
          { required: true, message: "请输入描述", trigger: "blur" }
        ],
      }
    };
  },
  watch: {
    $route: {
      handler(newValue) {
        if (newValue.name === 'Organ' || newValue.path === '/organ/organ') {
          this.getList();
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    // this.getList();
  },
  methods: {
    /** 查询组织机构列表 */
    getList() {
      // this.loading = true;
      listOrganPageBean(this.queryParams).then(response => {
        this.organList = response.data.list
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        orgName: null,
        orgDescribe: null,
        orgCode: null,
        levelNum: null,
        pid: null,
        levelOne: null,
        levelTwo: null,
        levelThree: null,
        createTime: null,
        createUser: null,
        updataTime: null,
        updataUser: null,
        deleted: null,
        status: null,
        sort: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      if (row) {
        // 列表中的新增， 为 二级或者三级
        this.form.pid = row.id
        this.form.levelNum = row.levelNum + 1
        if (row.levelNum === 1) {
          this.form.levelTwo = 1
        } else if (row.levelNum === 2) {
          this.form.levelThree = 1
        }
      } else {
        this.form.levelNum = 1
        this.form.levelOne = 1
      }
      this.open = true;
      this.title = "添加组织机构";
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getOrgan(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改组织机构";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateOrgan(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOrgan(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除当前数据？').then(function() {
        return delOrgan(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/organ/export', {
        ...this.queryParams
      }, `organ_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
