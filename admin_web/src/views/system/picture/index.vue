<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="110px">
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:picture:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:picture:remove']">删除</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="pictureList" @selection-change="handleSelectionChange" row-key="id">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="序号" align="center" prop="sort" width="80" />
            <el-table-column label="类型" align="center" prop="type" width="100" />
            <el-table-column label="图片预览" align="center" width="120">
                <template slot-scope="scope">
                    <el-image v-if="scope.row.url" :src="scope.row.url" style="width: 60px; height: 40px" fit="cover" :preview-src-list="[scope.row.url]"></el-image>
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column label="图片路径" align="center" prop="url" show-overflow-tooltip />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:picture:edit']">修改</el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:picture:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改轮播图信息对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="110px">
                <el-form-item label="序号" prop="sort">
                    <el-input-number v-model="form.sort" :min="1" :max="999" placeholder="请输入序号" style="width: 100%" />
                </el-form-item>
                <el-form-item label="类型" prop="type">
                    <el-radio-group v-model="form.type">
                        <el-radio label="图片">图片</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="添加图片" prop="url">
                    <image-upload v-model="form.url" :limit="1" />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listPicture, getPicture, delPicture, addPicture, updatePicture } from '@/api/system/picture';
import ImageUpload from '@/components/ImageUpload';

export default {
    name: 'Picture',
    components: {
        ImageUpload,
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 轮播图信息表格数据
            pictureList: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                url: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                sort: [{ required: true, message: '序号不能为空', trigger: 'blur' }],
                type: [{ required: true, message: '类型不能为空', trigger: 'change' }],
                url: [{ required: true, message: '图片不能为空', trigger: 'change' }],
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询轮播图信息列表 */
        getList() {
            this.loading = true;
            listPicture(this.queryParams).then((response) => {
                this.pictureList = response.rows.sort((a, b) => (a.sort || 0) - (b.sort || 0));
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                sort: null,
                type: '图片',
                url: null,
            };
            this.resetForm('form');
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = '新增轮播图';
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids;
            getPicture(id).then((response) => {
                this.form = response.data;
                this.open = true;
                this.title = '修改轮播图信息';
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.id != null) {
                        updatePicture(this.form).then((response) => {
                            this.$modal.msgSuccess('修改成功');
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addPicture(this.form).then((response) => {
                            this.$modal.msgSuccess('新增成功');
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal
                .confirm('是否确认删除轮播图信息编号为"' + ids + '"的数据项？')
                .then(function () {
                    return delPicture(ids);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
    },
};
</script>
