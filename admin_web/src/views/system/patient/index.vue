<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <!-- <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="证件号码" prop="idNum">
        <el-input
          v-model="queryParams.idNum"
          placeholder="请输入证件号码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="patientList">
      <el-table-column label="真实姓名" align="center" prop="name" />
      <el-table-column label="证件号码" align="center" prop="idNum" />
      <!-- <el-table-column label="年龄" align="center" prop="age" /> -->
      <el-table-column label="性别" align="center" prop="gender">
        <template slot-scope="scope">{{
          scope.row.gender === "0"
            ? "男"
            : scope.row.gender === "1"
            ? "女"
            : scope.row.gender
        }}</template>
      </el-table-column>
      <el-table-column label="手机号" align="center" prop="mobile" />
      <!-- <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="是否参加携带者筛查"
        align="center"
        prop="asCarrier"
        width="180"
      >
        <template slot-scope="scope">
          <span
            v-if="scope.row.asCarrier"
            class="primary-color pointer"
            @click="showDetail(scope.row, 1)"
            >是</span
          >
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column
        label="是否参加孕妇项目"
        align="center"
        prop="asGravida"
        width="180"
      >
        <template slot-scope="scope">
          <span
            v-if="scope.row.asGravida"
            class="primary-color pointer"
            @click="showDetail(scope.row, 2)"
            >是</span
          >
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column
        label="是否参加新生儿项目"
        align="center"
        prop="asNeonate"
        width="180"
      >
        <template slot-scope="scope">
          <span
            v-if="scope.row.asNeonate"
            class="primary-color pointer"
            @click="showDetail(scope.row, 3)"
            >是</span
          >
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column
        label="是否参加无创单病项目"
        align="center"
        prop="asNSD"
        width="180"
      >
        <template slot-scope="scope">
          <span
            v-if="scope.row.asNSD"
            class="primary-color pointer"
            @click="showDetail(scope.row, 4)"
            >是</span
          >
          <span v-else>否</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看患者信息 -->
    <el-dialog
      :title="patientDetailsTitle"
      :visible.sync="patientDetailsOpen"
      width="1000px"
      append-to-body
    >
      <!-- 携带者 详情 -->
      <screening
        v-if="patientDetailsData.patientType === 1"
        :showData="patientDetailsData"
      />
      <!-- 孕妇 详情 -->
      <gravida
        v-else-if="patientDetailsData.patientType === 2"
        :showData="patientDetailsData"
      />
      <!-- 新生儿 详情 -->
      <newbornbaby
        v-else-if="patientDetailsData.patientType === 3"
        :showData="patientDetailsData"
      />
      <!-- 无创单病 详情 -->
      <NIPT
        v-else-if="patientDetailsData.patientType === 4"
        :showData="patientDetailsData"
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  listPatient,
  getPatientBypatientIdAndpatientType,
} from "@/api/system/patient";

import screening from "./components/screening.vue";
import gravida from "./components/gravida.vue";
import newbornbaby from "./components/newbornbaby.vue";
import NIPT from "./components/NIPT.vue";

export default {
  name: "Patient",
  components: {
    screening,
    gravida,
    newbornbaby,
    NIPT,
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 患者管理表格数据
      patientList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        idNum: null,
        mobile: null,
      },
      selectRowData: {},
      patientDetailsOpen: false,
      patientDetailsTitle: "",
      patientDetailsData: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询患者管理列表 */
    getList() {
      this.loading = true;
      listPatient(this.queryParams).then((response) => {
        this.patientList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    showDetail(row, type) {
      const typeData = {
        1: "携带者筛查",
        2: "孕妇",
        3: "新生儿",
        4: "无创单病",
      };
      this.selectRowData = row;
      this.getPatientDetails(type);
      this.patientDetailsTitle = `${typeData[type]}信息`;
      this.patientDetailsOpen = true;
    },
    getPatientDetails(type) {
      getPatientBypatientIdAndpatientType({
        userId: this.selectRowData.userId,
        patientType: type,
      }).then((response) => {
        this.patientDetailsData = { ...response.data, ...{} };
      });
    },
  },
};
</script>

<style lang="scss">
.clinical-info-title {
  text-align: center;
  font-size: 20px;
  font-weight: 700;
  margin: 15px 0 12px;
}

.info-content {
  .info-item-view {
    line-height: 36px;
    font-size: 14px;
    margin-bottom: 6px;
    color: #606266;

    .item-title {
      font-weight: bold;
      margin-right: 12px;
    }
  }
}
</style>
