<!-- 携带者 -->
<template>
  <div>
    <el-row
      :gutter="10"
      class="info-content"
    >
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">医院: </span>{{ showData.hospName }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">医生: </span>{{ showData.doctName }}</div>
      </el-col>
    </el-row>

    <div class="clinical-info-title">母亲信息</div>
    <el-row
      :gutter="10"
      class="info-content"
    >
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">母亲姓名: </span>{{ showData.motherName }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">母亲年龄: </span>{{ showData.motherAge }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">证件类型: </span>{{ showData.motherType }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">证件号码: </span>{{ showData.motherNum }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">联系方式: </span>{{ showData.motherMobile }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">出生日期: </span>{{ showData.motherBirthday }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">民族: </span>{{ showData.motherClan }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">籍贯: </span>{{ showData.motherPlace }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">是否上保险: </span>{{ showData.motherInsure || showData.motherInsure === '1' || showData.motherInsure === 1 ? '是' : '否' }}</div>
      </el-col>
    </el-row>

    <div class="clinical-info-title">父亲信息</div>
    <el-row
      :gutter="10"
      class="info-content"
    >
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">父亲姓名: </span>{{ showData.fatherName }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">父亲年龄: </span>{{ showData.fatherAge }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">证件类型: </span>{{ showData.fatherType }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">证件号码: </span>{{ showData.fatherNum }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">联系方式: </span>{{ showData.fatherMobile }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">出生日期: </span>{{ showData.fatherBirthday }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">民族: </span>{{ showData.fatherClan }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">籍贯: </span>{{ showData.fatherPlace }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">是否上保险: </span>{{ showData.fatherInsure || showData.fatherInsure === '1' || showData.fatherInsure === 1 ? '是' : '否' }}</div>
      </el-col>
    </el-row>
  </div>
</template>

<script>

export default {
  name: '',
  props: {
    showData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  }
}
</script>
