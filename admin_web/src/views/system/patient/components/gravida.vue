<!-- 孕妇 -->
<template>
  <div>
    <el-row
      :gutter="10"
      class="info-content"
    >
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">医院: </span>{{ showData.hospName }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">医生: </span>{{ showData.doctName }}</div>
      </el-col>
    </el-row>

    <div class="clinical-info-title">基础信息</div>
    <el-row
      :gutter="10"
      class="info-content"
    >
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">姓名: </span>{{ showData.gName }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">年龄: </span>{{ showData.gAge }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">证件类型: </span>{{ showData.docType }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">证件号码: </span>{{ showData.idNum }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">联系方式: </span>{{ showData.gMobile }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">家属名称: </span>{{ showData.familyName }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">家属联系方式: </span>{{ showData.familyMobile }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">紧急联系人: </span>{{ showData.emergencyName }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">紧急联系人手机号: </span>{{ showData.emergencyMobile }}</div>
      </el-col>
    </el-row>
    <div class="clinical-info-title">其他信息</div>
    <el-row
      :gutter="10"
      class="info-content"
    >
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">性别: </span>{{ showData.gender }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">出生日期: </span>{{ showData.gBirthday }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">民族: </span>{{ showData.gClan }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">籍贯: </span>{{ showData.gPlace }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">孕周: </span>{{ showData.gWeek }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">孕天: </span>{{ showData.gDay }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">月经周期: </span>{{ showData.menstrualCycle }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">末次月经时间: </span>{{ showData.menstruationEndTime }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">身高: </span>{{ showData.gHeight ? `${showData.gHeight}cm` : '' }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">体重: </span>{{ showData.gWeight ? `${showData.gWeight}kg` : '' }}</div>
      </el-col>
      <el-col :span="8">
        <div class="info-item-view"><span class="item-title">是否上保险: </span>{{ showData.gInsure || showData.gInsure === '1' || showData.gInsure === 1 ? '是' : '否' }}</div>
      </el-col>
      <el-col :span="24">
        <div class="info-item-view"><span class="item-title">地址: </span>{{ showData.addressDetails }}</div>
      </el-col>
    </el-row>
  </div>
</template>

<script>

export default {
  name: '',
  props: {
    showData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  }
}
</script>
