<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="医生编号" prop="dCode">
        <el-input
          v-model="queryParams.dCode"
          placeholder="请输入医生编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="性别" prop="gender">
        <el-input
          v-model="queryParams.gender"
          placeholder="请输入性别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="医院id" prop="hospId">
        <el-input
          v-model="queryParams.hospId"
          placeholder="请输入医院id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="所属医院" prop="hospName">
        <el-input
          v-model="queryParams.hospName"
          placeholder="请输入所属医院"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="组织id" prop="orgId">
        <el-input
          v-model="queryParams.orgId"
          placeholder="请输入组织id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="科室编号" prop="depId">
        <el-input
          v-model="queryParams.depId"
          placeholder="请输入科室编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="科室名称" prop="depName">
        <el-input
          v-model="queryParams.depName"
          placeholder="请输入科室名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="职级" prop="job">
        <el-input
          v-model="queryParams.job"
          placeholder="请输入职级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="管理员id" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入管理员id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="是否管理员" prop="manageAs">
        <el-input
          v-model="queryParams.manageAs"
          placeholder="请输入是否管理员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="携带者筛选项目" prop="carryAs">
        <el-input
          v-model="queryParams.carryAs"
          placeholder="请输入携带者筛选项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="孕妇项目" prop="gravida">
        <el-input
          v-model="queryParams.gravida"
          placeholder="请输入孕妇项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="新生儿项目" prop="neonate">
        <el-input
          v-model="queryParams.neonate"
          placeholder="请输入新生儿项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="${comment}" prop="createUser">
        <el-input
          v-model="queryParams.createUser"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="${comment}" prop="updataUser">
        <el-input
          v-model="queryParams.updataUser"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="${comment}" prop="updataTime">
        <el-date-picker clearable
          v-model="queryParams.updataTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择${comment}">
        </el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="${comment}" prop="deleted">
        <el-input
          v-model="queryParams.deleted"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:doctor:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:doctor:edit']"
        >修改</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:doctor:remove']"
        >删除</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:doctor:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="doctorList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
      <!-- <el-table-column label="医生编号" align="center" prop="dCode" /> -->
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="性别" align="center" prop="gender" />
      <!-- <el-table-column label="医院id" align="center" prop="hospId" /> -->
      <el-table-column label="所属医院" align="center" prop="hospName" />
      <!-- <el-table-column label="组织id" align="center" prop="orgId" /> -->
      <!-- <el-table-column label="科室编号" align="center" prop="depId" /> -->
      <!-- <el-table-column label="科室名称" align="center" prop="depName" /> -->
      <!-- <el-table-column label="职级" align="center" prop="job" /> -->
      <!-- <el-table-column label="管理员id" align="center" prop="userId" /> -->
      <!-- <el-table-column label="是否管理员" align="center" prop="manageAs" /> -->
      <!-- <el-table-column label="携带者筛选项目" align="center" prop="carryAs" /> -->
      <!-- <el-table-column label="孕妇项目" align="center" prop="gravida" /> -->
      <!-- <el-table-column label="新生儿项目" align="center" prop="neonate" /> -->
      <el-table-column label="手机号" align="center" prop="mobile" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="${comment}" align="center" prop="createUser" /> -->
      <!-- <el-table-column label="${comment}" align="center" prop="updataUser" /> -->
      <!-- <el-table-column label="${comment}" align="center" prop="updataTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updataTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="${comment}" align="center" prop="deleted" /> -->
      <!-- <el-table-column label="${comment}" align="center" prop="status" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDetail(scope.row)"
            v-hasPermi="['system:doctor:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:doctor:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:doctor:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看医生信息 -->
    <el-dialog title="医生详情" :visible.sync="openDoctorDetail" width="500px" append-to-body>
      <!-- <div class="doctor-detail-title">医生信息</div> -->
      <el-row :gutter="20" style="line-height: 36px;">
        <el-col :span="12">
          <span>医生姓名: </span>
          <span>{{ selectDoctorData.name }}</span>
        </el-col>
        <el-col :span="12">
          <span>性别: </span>
          <span>{{ selectDoctorData.gender }}</span>
        </el-col>
        <el-col :span="12">
          <span>所属医院: </span>
          <span>{{ selectDoctorData.hospName }}</span>
        </el-col>
        <el-col :span="12">
          <span>职称: </span>
          <span>{{ selectDoctorData.job }}</span>
        </el-col>
        <el-col :span="12">
          <span>科室: </span>
          <span>{{ selectDoctorData.depName }}</span>
        </el-col>
        <el-col :span="12">
          <span>手机号: </span>
          <span>{{ selectDoctorData.mobile }}</span>
        </el-col>
        <el-col :span="12">
          <span>管理员: </span>
          <span>{{ selectDoctorData.manageAs ? '是':'否' }}</span>
        </el-col>
        <el-col :span="12">
          <span>项目: </span>
          <span>
            <span v-if="selectDoctorData.carryAs">携带者筛选项目</span>
            <span v-if="selectDoctorData.carryAs && selectDoctorData.gravida || selectDoctorData.carryAs && selectDoctorData.neonate">、</span>
            <span v-if="selectDoctorData.gravida">孕妇项目</span>
            <span v-if="selectDoctorData.neonate && selectDoctorData.gravida">、</span>
            <span v-if="selectDoctorData.neonate">新生儿项目</span>
          </span>
        </el-col>
      </el-row>

      <!-- <div class="doctor-detail-title" style="margin-top: 20px; border-top: 1px solid #bfbfbf;">患者信息</div>
      <el-table v-loading="loading" :data="selectDoctorData.patientVOSList" style="max-height: 400px;">
        <el-table-column label="患者姓名" align="center" prop="name" />
        <el-table-column label="患者性别" align="center" prop="gender">
          <template slot-scope="scope">{{ scope.row.gender === '0' ? '男' : scope.row.gender === '1' ? '女' : scope.row.gender }}</template>
        </el-table-column>
        <el-table-column label="患者年龄" align="center" prop="age" />
        <el-table-column label="联系电话" align="center" prop="mobile" />
      </el-table> -->

      <!-- <pagination
        v-show="patientTotal>0"
        :total="patientTotal"
        :page.sync="patientParams.pageNum"
        :limit.sync="patientParams.pageSize"
        @pagination="getPatientList"
      /> -->
    </el-dialog>

    <!-- 添加或修改医生管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="医生编号" prop="dCode">
          <el-input v-model="form.dCode" placeholder="请输入医生编号" />
        </el-form-item> -->
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <!-- <el-input v-model="form.gender" placeholder="请输入性别" /> -->
          <el-select
            v-model="form.gender"
            placeholder="请选择"
          >
            <el-option label="男" value="男" />
            <el-option label="女" value="女" />
          </el-select>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="form.mobile" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" placeholder="请输入密码" type="password" show-password/>
        </el-form-item>
        <el-form-item label="所属医院" prop="hospId">
          <el-select
            v-model="form.hospId"
            placeholder="请选择所属医院"
            clearable
            filterable
            style="width: 100%;"
            >
            <el-option
              v-for="item in hospitalOption"
              :key="item.id"
              :label="item.hospName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="组织id" prop="orgId">
          <el-input v-model="form.orgId" placeholder="请输入组织id" />
        </el-form-item> -->
        <!-- <el-form-item label="科室编号" prop="depId">
          <el-input v-model="form.depId" placeholder="请输入科室编号" />
        </el-form-item> -->
        <el-form-item label="科室名称" prop="depName">
          <el-input v-model="form.depName" placeholder="请输入科室名称" />
        </el-form-item>
        <el-form-item label="职级" prop="job">
          <el-input v-model="form.job" placeholder="请输入职级" />
        </el-form-item>
        <!-- <el-form-item label="管理员id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入管理员id" />
        </el-form-item> -->
        <el-form-item label="管理员" prop="manageAs">
          <!-- <el-input v-model="form.manageAs" placeholder="请输入是否管理员" /> -->
          <el-radio-group v-model="form.manageAs" @change="changeManageAs">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="项目" prop="selectTestingItem">
          <el-checkbox-group v-model="form.selectTestingItem">
            <el-checkbox label="carryAs">携带者筛选项目</el-checkbox>
            <el-checkbox label="gravida">孕妇项目</el-checkbox>
            <el-checkbox label="neonate">新生儿项目</el-checkbox>
            <el-checkbox label="nsd">无创单病</el-checkbox>
          </el-checkbox-group>
          <!-- <el-checkbox v-model="form.carryAs">携带者筛选项目</el-checkbox>
          <el-checkbox v-model="form.gravida">孕妇项目</el-checkbox>
          <el-checkbox v-model="form.neonate">新生儿项目</el-checkbox> -->
        </el-form-item>
        <!-- <el-form-item label="携带者筛选项目" prop="carryAs">
          <el-input v-model="form.carryAs" placeholder="请输入携带者筛选项目" />
        </el-form-item> -->
        <!-- <el-form-item label="孕妇项目" prop="gravida">
          <el-input v-model="form.gravida" placeholder="请输入孕妇项目" />
        </el-form-item> -->
        <!-- <el-form-item label="新生儿项目" prop="neonate">
          <el-input v-model="form.neonate" placeholder="请输入新生儿项目" />
        </el-form-item> -->
        <!-- <el-form-item label="${comment}" prop="createUser">
          <el-input v-model="form.createUser" placeholder="请输入${comment}" />
        </el-form-item> -->
        <!-- <el-form-item label="${comment}" prop="updataUser">
          <el-input v-model="form.updataUser" placeholder="请输入${comment}" />
        </el-form-item> -->
        <!-- <el-form-item label="${comment}" prop="updataTime">
          <el-date-picker clearable
            v-model="form.updataTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择${comment}">
          </el-date-picker>
        </el-form-item> -->
        <!-- <el-form-item label="${comment}" prop="deleted">
          <el-input v-model="form.deleted" placeholder="请输入${comment}" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDoctor, getDoctor, delDoctor, addDoctor, updateDoctor } from "@/api/system/doctor";
import { resetUserPwd } from "@/api/system/user";
import { listHospital } from "@/api/system/hospital";

export default {
  name: "Doctor",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 医生管理表格数据
      doctorList: [],
      // 展示的医生详情
      selectDoctorData: {},
      openDoctorDetail: false,
      hospitalOption: [],
      patientList: [],
      patientTotal: 0,
      patientParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dCode: null,
        name: null,
        gender: null,
        hospId: null,
        hospName: null,
        orgId: null,
        depId: null,
        depName: null,
        job: null,
        userId: null,
        manageAs: null,
        carryAs: null,
        gravida: null,
        neonate: null,
        selectTestingItem: [],
        createUser: null,
        updataUser: null,
        updataTime: null,
        deleted: null,
        status: null,
        mobile: null
      },
      testingItems: ['carryAs', 'gravida', 'neonate','nsd'],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "请输入姓名", trigger: "blur" },
          { min: 1, max: 30, message: '姓名长度最大不能超过 30 个字符', trigger: 'blur' }
        ],
        gender: [
          { required: true, message: "请选择性别", trigger: "change" }
        ],
        mobile: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
        ],
        hospId: [
          { required: true, message: "请选择所属医院", trigger: "change" }
        ],
        depName: [
          { required: true, message: "请输入科室名称", trigger: "blur" },
          { min: 1, max: 30, message: '科室名称长度最大不能超过 30 个字符', trigger: 'blur' }
        ],
        job: [
          { required: true, message: "请输入职级", trigger: "change" },
          { min: 1, max: 30, message: '职级长度最大不能超过 30 个字符', trigger: 'blur' }
        ],
        manageAs: [
          { required: true, message: "请选择是否是管理员", trigger: "change" }
        ],
        selectTestingItem: [
          { required: true, message: "至少选择一个项目", trigger: "change" }
        ],
      },
      userId:0
    };
  },
  watch: {
    $route: {
      handler(newValue) {
        if (newValue.name === 'Doctor' || newValue.path === '/doctor/doctor') {
          this.getList()
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.getList();
    this.getListHospital()
  },
  methods: {
    /** 查询医生管理列表 */
    getList() {
      this.loading = true;
      listDoctor(this.queryParams).then(response => {
        this.doctorList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 查询医院管理列表 */
    getListHospital() {
      listHospital({
        pageNum: 1,
        pageSize: 1000,
      }).then(response => {
        this.hospitalOption = response.data.list || [];
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        // dCode: null,
        name: null,
        gender: null,
        hospId: null,
        hospName: null,
        // orgId: null,
        // depId: null,
        depName: null,
        job: null,
        userId: null,
        manageAs: null,
        carryAs: null,
        gravida: null,
        neonate: null,
        nsd:null,
        selectTestingItem: [],
        // createUser: null,
        // createTime: null,
        // updataUser: null,
        // updataTime: null,
        // deleted: null,
        // status: null,
        mobile: null,
        password: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    handleDetail(row) {
      const id = row.id || this.ids
      getDoctor(id).then(response => {
        this.selectDoctorData = response.data;
        this.openDoctorDetail = true
      });
    },
    getPatientList() {

    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加医生管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.userId = row.userId
      this.reset();
      const id = row.id || this.ids
      getDoctor(id).then(response => {
        const testingArr = []
        this.testingItems.forEach(ele => {
          if (response.data[ele]) {
            testingArr.push(ele)
          }
        })
        const resData = response.data
        this.form = { ...response.data, ...{ selectTestingItem: testingArr}};
        this.form = {
          id: resData.id,
          name: resData.name,
          gender: resData.gender,
          hospId: resData.hospId,
          depName: resData.depName,
          job: resData.job,
          userId: resData.userId,
          manageAs: resData.manageAs,
          carryAs: resData.carryAs,
          gravida: resData.gravida,
          nsd: resData.nsd,
          neonate: resData.neonate,
          selectTestingItem: testingArr,
          mobile: resData.mobile,
          password:resData.password
        };
        this.open = true;
        this.title = "修改医生管理";
      });
    },
    changeManageAs(val) {
      if (val) {
        this.form.selectTestingItem = JSON.parse(JSON.stringify(this.testingItems))
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const params = JSON.parse(JSON.stringify(this.form))
          const seletHosp = this.hospitalOption.filter(ele => {
            return ele.id === this.form.hospId
          })[0]
          params['hospName'] = seletHosp.hospName
          params['orgId'] = seletHosp.orgId
          this.testingItems.forEach(ele => {
            params[ele] = this.form.selectTestingItem.includes(ele)
          })
          delete params['selectTestingItem']
          if (params.id != null) {
            updateDoctor(params).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              // resetUserPwd(this.userId, this.form.password).then(response => {
              //   this.$modal.msgSuccess("修改成功");
              //   this.open = false;
              //   this.getList();
              // });

            });
          } else {
            delete params['id']
            addDoctor(params).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除当前数据？').then(function() {
        return delDoctor(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/doctor/export', {
        ...this.queryParams
      }, `doctor_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style lang="scss">
.doctor-detail-title {
  font-weight: bold;
  font-size: 16px;
  line-height: 36px;
  text-align: center;
}
</style>